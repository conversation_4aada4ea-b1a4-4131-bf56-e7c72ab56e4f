import argparse
import sys
from typing import List, Optional, Tuple, Dict

import numpy as np
from scipy import ndimage

try:
    import SimpleITK as sitk
except Exception as exc:  # pragma: no cover
    print("[Error] Failed to import SimpleITK. Please install it: pip install SimpleITK", file=sys.stderr)
    raise

try:
    import vtk
    from vtk.util import numpy_support
except Exception as exc:  # pragma: no cover
    print("[Error] Failed to import VTK. Please install it: pip install vtk", file=sys.stderr)
    raise


def read_label_image(path: str) -> sitk.Image:
    image = sitk.ReadImage(path)
    if image.GetNumberOfComponentsPerPixel() != 1:
        raise ValueError("Expected a scalar label image (single component per voxel).")
    # Force to an integer type suitable for discrete marching cubes
    pixel_id = image.GetPixelID()
    if pixel_id not in (
        sitk.sitkUInt8,
        sitk.sitkUInt16,
        sitk.sitkUInt32,
        sitk.sitkInt16,
        sitk.sitkInt32,
    ):
        image = sitk.Cast(image, sitk.sitkUInt16)
    return image


def filter_image_labels(image: sitk.Image, keep_labels: List[int]) -> sitk.Image:
    """
    过滤图像，只保留指定的标签，其他标签设为0。
    这样可以减少后续VTK处理的数据量。
    """
    print(f"Filtering image to keep only labels: {keep_labels}")
    
    # 获取数组
    array = sitk.GetArrayFromImage(image)
    original_unique = np.unique(array)
    print(f"Original labels in image: {original_unique}")
    
    # 创建掩码，只保留指定标签
    mask = np.zeros_like(array, dtype=bool)
    for label in keep_labels:
        mask |= (array == label)
    
    # 应用掩码
    filtered_array = np.where(mask, array, 0)
    filtered_unique = np.unique(filtered_array)
    print(f"After filtering: {filtered_unique}")
    
    # 转换回SimpleITK图像
    filtered_image = sitk.GetImageFromArray(filtered_array)
    filtered_image.CopyInformation(image)  # 复制spacing, origin, direction等信息
    
    return filtered_image


def sitk_image_to_vtk_image(image: sitk.Image) -> vtk.vtkImageData:
    """
    Convert SimpleITK image to VTK image with proper coordinate handling.
    SimpleITK uses (x,y,z) ordering while array is (z,y,x).
    VTK expects point data in Fortran order (x varies fastest).
    """
    # Get image properties
    size = image.GetSize()  # (x, y, z)
    spacing = image.GetSpacing()  # (x, y, z) 
    origin = image.GetOrigin()  # (x, y, z)
    
    # Get numpy array - this is in (z, y, x) order
    array_zyx = sitk.GetArrayFromImage(image)
    
    print(f"Original image size (x,y,z): {size}")
    print(f"Array shape (z,y,x): {array_zyx.shape}")
    print(f"Spacing: {spacing}")
    print(f"Origin: {origin}")
    print(f"Data type: {array_zyx.dtype}")
    print(f"Value range: [{array_zyx.min()}, {array_zyx.max()}]")
    
    # Ensure integer type for labels
    if not np.issubdtype(array_zyx.dtype, np.integer):
        array_zyx = array_zyx.astype(np.uint16)
    
    # Create VTK image
    vtk_image = vtk.vtkImageData()
    vtk_image.SetDimensions(size[0], size[1], size[2])  # (x, y, z)
    vtk_image.SetSpacing(spacing[0], spacing[1], spacing[2])
    vtk_image.SetOrigin(origin[0], origin[1], origin[2])
    
    # Convert array to VTK format
    # Need to transpose from (z,y,x) to (x,y,z) and flatten in Fortran order
    array_xyz = np.transpose(array_zyx, (2, 1, 0))  # (x, y, z)
    flat_array = np.ravel(array_xyz, order='F')  # Fortran order for VTK
    
    vtk_array = numpy_support.numpy_to_vtk(flat_array, deep=True)
    vtk_array.SetName("Labels")
    vtk_image.GetPointData().SetScalars(vtk_array)
    
    return vtk_image


def compute_unique_labels(image: sitk.Image, exclude_labels: Optional[List[int]] = None) -> List[int]:
    arr = sitk.GetArrayFromImage(image)
    unique = np.unique(arr)
    if exclude_labels:
        unique = np.array([v for v in unique if v not in set(exclude_labels)], dtype=unique.dtype)
    # Convert to Python ints and sort
    labels = sorted(int(v) for v in unique.tolist())
    return labels


def build_lookup_table(labels: List[int]) -> vtk.vtkLookupTable:
    if not labels:
        lut = vtk.vtkLookupTable()
        lut.Build()
        return lut

    max_label = max(labels)
    lut = vtk.vtkLookupTable()
    # Build table large enough to index directly by label value
    lut.SetNumberOfTableValues(max_label + 1)
    lut.SetRange(0.0, float(max_label))
    lut.SetRampToLinear()
    lut.Build()

    named_colors = vtk.vtkNamedColors()

    # Predefine some pleasant distinct colors; fallback to random if too many labels
    predefined_color_names = [
        "Tomato",
        "Banana",
        "DodgerBlue",
        "LimeGreen",
        "Magenta",
        "Orange",
        "Cyan",
        "Yellow",
        "DeepPink",
        "MediumPurple",
        "Turquoise",
        "SandyBrown",
        "Chartreuse",
        "Orchid",
        "SteelBlue",
        "OliveDrab",
    ]

    color_index = 0
    used = set()
    for label in labels:
        if label < 0:
            continue
        if color_index < len(predefined_color_names):
            rgb = named_colors.GetColor3d(predefined_color_names[color_index])
            color_index += 1
        else:
            # Random-ish distinct colors based on label
            rng = np.random.default_rng(seed=label)
            rgb = (float(rng.random()), float(rng.random()), float(rng.random()))
        lut.SetTableValue(label, rgb[0], rgb[1], rgb[2], 1.0)
        used.add(label)

    # Make any non-used entries transparent
    for label in range(max_label + 1):
        if label not in used:
            lut.SetTableValue(label, 0.0, 0.0, 0.0, 0.0)

    return lut


def create_surface_for_label(vtk_image: vtk.vtkImageData, label: int, color: Tuple[float, float, float], fast_mode: bool = True) -> vtk.vtkActor:
    """Create a surface actor for a single label value."""
    print(f"Processing label {label}...")
    
    # Use marching cubes for single label
    marching_cubes = vtk.vtkMarchingCubes()
    marching_cubes.SetInputData(vtk_image)
    marching_cubes.SetValue(0, label)
    marching_cubes.ComputeNormalsOn()
    marching_cubes.ComputeScalarsOff()
    marching_cubes.Update()
    
    polydata = marching_cubes.GetOutput()
    print(f"  Generated {polydata.GetNumberOfPoints()} points, {polydata.GetNumberOfCells()} cells")
    
    if polydata.GetNumberOfPoints() == 0:
        print(f"  Warning: No surface generated for label {label}")
        return None
    
    # Optional cleaning and decimation for faster rendering
    last_filter = marching_cubes
    
    if fast_mode:
        # Light decimation to reduce polygon count for faster rendering
        decimate = vtk.vtkDecimatePro()
        decimate.SetInputConnection(marching_cubes.GetOutputPort())
        decimate.SetTargetReduction(0.3)  # Reduce by 30%
        decimate.SetPreserveTopology(True)
        decimate.Update()
        last_filter = decimate
        
        reduced_points = decimate.GetOutput().GetNumberOfPoints()
        print(f"  Decimated to {reduced_points} points (30% reduction for faster rendering)")
    else:
        print(f"  High quality mode: keeping all {polydata.GetNumberOfPoints()} points")
    
    # Clean the polydata
    cleaner = vtk.vtkCleanPolyData()
    cleaner.SetInputConnection(last_filter.GetOutputPort())
    cleaner.Update()
    
    # Create mapper
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(cleaner.GetOutputPort())
    mapper.ScalarVisibilityOff()  # Use solid color
    
    # Create actor
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    actor.GetProperty().SetColor(color[0], color[1], color[2])
    actor.GetProperty().SetOpacity(0.8)
    
    return actor


def voxel_to_world_coordinates(image: sitk.Image, voxel_coords: Tuple[int, int, int]) -> Tuple[float, float, float]:
    """
    将体素坐标转换为世界坐标
    
    Args:
        image: SimpleITK图像
        voxel_coords: (i, j, k) 体素坐标，零基索引
    
    Returns:
        (x, y, z) 世界坐标 (mm)
    """
    spacing = image.GetSpacing()  # (x, y, z)
    origin = image.GetOrigin()    # (x, y, z)
    
    # 体素坐标 (i, j, k) 对应 (x, y, z) 方向
    world_x = origin[0] + voxel_coords[0] * spacing[0]
    world_y = origin[1] + voxel_coords[1] * spacing[1]
    world_z = origin[2] + voxel_coords[2] * spacing[2]
    
    return (world_x, world_y, world_z)


def detect_vertebra_superior_endplate_center(image: sitk.Image, label: int) -> Optional[Tuple[float, float, float]]:
    """
    检测椎体的上终板中点。
    
    算法思路：
    1. 提取指定标签的椎体区域
    2. 分析椎体的Z轴分布，找到最上方的层面
    3. 在上方层面中找到最大连通区域的重心作为上终板中点
    
    Returns:
        (x, y, z) 世界坐标系中的上终板中点，如果检测失败返回None
    """
    print(f"Detecting superior endplate center for vertebra label {label}...")
    
    # 获取图像数组和属性
    array = sitk.GetArrayFromImage(image)  # (z, y, x)
    spacing = image.GetSpacing()  # (x, y, z)
    origin = image.GetOrigin()    # (x, y, z)
    
    # 创建该标签的二值化掩码
    mask = (array == label)
    
    if not np.any(mask):
        print(f"  Warning: No voxels found for label {label}")
        return None
    
    # 找到椎体在Z轴的范围
    z_indices = np.where(np.any(mask, axis=(1, 2)))[0]
    if len(z_indices) == 0:
        return None
    
    z_min, z_max = z_indices[0], z_indices[-1]
    print(f"  Vertebra Z range: {z_min} to {z_max} (array indices)")
    
    # 定义上终板区域：椎体上方的一定比例
    # 对于腰椎，上终板通常在椎体的上1/4到1/3区域
    upper_portion = 0.25  # 使用上25%区域
    z_upper_range = int((z_max - z_min) * upper_portion)
    z_upper_start = max(z_min, z_max - z_upper_range)
    
    print(f"  Analyzing superior endplate region: Z {z_upper_start} to {z_max}")
    
    # 在上终板区域寻找最大的连通区域
    best_center = None
    max_area = 0
    
    for z in range(z_upper_start, z_max + 1):
        slice_mask = mask[z, :, :]
        if not np.any(slice_mask):
            continue
        
        # 找到连通组件
        labeled_slice, num_components = ndimage.label(slice_mask)
        
        if num_components == 0:
            continue
        
        # 找到最大的连通组件
        for comp_id in range(1, num_components + 1):
            component_mask = (labeled_slice == comp_id)
            area = np.sum(component_mask)
            
            if area > max_area:
                max_area = area
                # 计算重心
                y_indices, x_indices = np.where(component_mask)
                center_x_idx = np.mean(x_indices)
                center_y_idx = np.mean(y_indices)
                center_z_idx = z
                
                # 转换为世界坐标
                center_x = origin[0] + center_x_idx * spacing[0]
                center_y = origin[1] + center_y_idx * spacing[1]
                center_z = origin[2] + center_z_idx * spacing[2]
                
                best_center = (center_x, center_y, center_z)
    
    if best_center:
        print(f"  Superior endplate center found at: ({best_center[0]:.1f}, {best_center[1]:.1f}, {best_center[2]:.1f})")
        return best_center
    else:
        print(f"  Warning: Could not detect superior endplate center for label {label}")
        return None


def create_sphere_actor(center: Tuple[float, float, float], radius: float = 2.0, color: Tuple[float, float, float] = (1.0, 1.0, 1.0)) -> vtk.vtkActor:
    """
    创建一个球体Actor来标记点位置
    
    Args:
        center: 球心坐标 (x, y, z)
        radius: 球体半径
        color: RGB颜色 (默认白色)
    """
    sphere = vtk.vtkSphereSource()
    sphere.SetCenter(center[0], center[1], center[2])
    sphere.SetRadius(radius)
    sphere.SetPhiResolution(20)
    sphere.SetThetaResolution(20)
    
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(sphere.GetOutputPort())
    
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    actor.GetProperty().SetColor(color[0], color[1], color[2])
    actor.GetProperty().SetSpecular(0.3)
    actor.GetProperty().SetSpecularPower(30)
    
    return actor


def get_point_colors(num_points: int) -> List[Tuple[float, float, float]]:
    """Generate distinct colors for multiple points."""
    if num_points <= 1:
        return [(1.0, 0.0, 0.0)]  # Red for single point
    
    colors = []
    named_colors = vtk.vtkNamedColors()
    
    # Predefined distinct colors for multiple points
    color_names = [
        "Red", "Blue", "Green", "Orange", "Purple", "Yellow",
        "Cyan", "Magenta", "Pink", "Lime", "Turquoise", "Gold",
        "Coral", "Navy", "Olive", "Maroon", "Teal", "Silver"
    ]
    
    for i in range(num_points):
        if i < len(color_names):
            color = named_colors.GetColor3d(color_names[i])
        else:
            # Generate random color based on index for consistency
            np.random.seed(i + 100)  # Offset to avoid collision with label colors
            color = (np.random.random(), np.random.random(), np.random.random())
        colors.append(color)
    
    return colors


def save_points_to_nifti(image: sitk.Image, points: List[Tuple[int, int, int]], 
                         point_labels: List[int], output_path: str) -> None:
    """
    将点标记保存为NIfTI文件，每个点用指定的标签值标记
    
    Args:
        image: 原始SimpleITK图像（用于获取坐标系信息）
        points: 体素坐标列表 [(i1, j1, k1), (i2, j2, k2), ...]
        point_labels: 对应的标签值列表 [label1, label2, ...]
        output_path: 输出文件路径
    """
    print(f"\n=== Saving Points to NIfTI File ===")
    print(f"Output file: {output_path}")
    
    # 获取图像属性
    size = image.GetSize()
    spacing = image.GetSpacing()
    origin = image.GetOrigin()
    direction = image.GetDirection()
    
    # 创建新的数组，初始化为0
    array = np.zeros(size[::-1], dtype=np.uint16)  # 注意：SimpleITK数组是(z,y,x)顺序
    
    # 在指定位置设置标签值
    for i, (voxel_coords, label) in enumerate(zip(points, point_labels)):
        i_idx, j_idx, k_idx = voxel_coords
        
        # 检查坐标是否在范围内
        if (0 <= i_idx < size[0] and 0 <= j_idx < size[1] and 0 <= k_idx < size[2]):
            # 在体素位置设置标签值
            array[k_idx, j_idx, i_idx] = label
            print(f"Point {i+1}: Voxel ({i_idx}, {j_idx}, {k_idx}) -> Label {label}")
        else:
            print(f"Warning: Point {i+1} coordinates ({i_idx}, {j_idx}, {k_idx}) out of bounds!")
    
    # 创建新的SimpleITK图像
    output_image = sitk.GetImageFromArray(array)
    output_image.SetSpacing(spacing)
    output_image.SetOrigin(origin)
    output_image.SetDirection(direction)
    
    # 保存文件
    sitk.WriteImage(output_image, output_path)
    print(f"Successfully saved NIfTI file with {len(points)} point markers")


def save_points_to_text(points: List[Tuple[int, int, int]], 
                       point_labels: List[int], 
                       world_coords: List[Tuple[float, float, float]],
                       output_path: str) -> None:
    """
    将点坐标信息保存为文本文件
    
    Args:
        points: 体素坐标列表
        point_labels: 标签值列表
        world_coords: 世界坐标列表
        output_path: 输出文件路径
    """
    print(f"\n=== Saving Points to Text File ===")
    print(f"Output file: {output_path}")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# Point Markers for Non-rigid Registration\n")
        f.write("# Format: Point_ID, Voxel_Coordinates(i,j,k), World_Coordinates(x,y,z)_mm, Label_Value\n")
        f.write("# Generated by CT Segmentation Labels Visualization Tool\n\n")
        
        for i, (voxel_coords, label, world_coord) in enumerate(zip(points, point_labels, world_coords)):
            f.write(f"Point_{i+1:02d}: ")
            f.write(f"Voxel({voxel_coords[0]:4d}, {voxel_coords[1]:4d}, {voxel_coords[2]:4d}) ")
            f.write(f"World({world_coord[0]:8.2f}, {world_coord[1]:8.2f}, {world_coord[2]:8.2f}) ")
            f.write(f"Label={label:3d}\n")
    
    print(f"Successfully saved text file with {len(points)} point records")


def save_label_with_points_to_nifti(image: sitk.Image, label_value: int, 
                                   points: List[Tuple[int, int, int]], 
                                   point_labels: List[int], output_path: str) -> None:
    """
    将指定标签和点标记一起保存为NIfTI文件
    
    Args:
        image: 原始SimpleITK图像（用于获取坐标系信息）
        label_value: 要保存的标签值
        points: 体素坐标列表 [(i1, j1, k1), (i2, j2, k2), ...]
        point_labels: 对应的标签值列表 [label1, label2, ...]
        output_path: 输出文件路径
    """
    print(f"\n=== Saving Label {label_value} with Points to NIfTI File ===")
    print(f"Output file: {output_path}")
    
    # 获取图像属性
    size = image.GetSize()
    spacing = image.GetSpacing()
    origin = image.GetOrigin()
    direction = image.GetDirection()
    
    # 创建新的数组，初始化为0
    array = np.zeros(size[::-1], dtype=np.uint16)  # 注意：SimpleITK数组是(z,y,x)顺序
    
    # 首先提取指定标签的区域
    original_array = sitk.GetArrayFromImage(image)
    label_mask = (original_array == label_value)
    array[label_mask] = label_value
    
    print(f"Extracted label {label_value} region: {np.sum(label_mask)} voxels")
    
    # 在指定位置设置点标记标签值
    for i, (voxel_coords, point_label) in enumerate(zip(points, point_labels)):
        i_idx, j_idx, k_idx = voxel_coords
        
        # 检查坐标是否在范围内
        if (0 <= i_idx < size[0] and 0 <= j_idx < size[1] and 0 <= k_idx < size[2]):
            # 在体素位置设置点标记标签值
            array[k_idx, j_idx, i_idx] = point_label
            print(f"Point {i+1}: Voxel ({i_idx}, {j_idx}, {k_idx}) -> Label {point_label}")
        else:
            print(f"Warning: Point {i+1} coordinates ({i_idx}, {j_idx}, {k_idx}) out of bounds!")
    
    # 创建新的SimpleITK图像
    output_image = sitk.GetImageFromArray(array)
    output_image.SetSpacing(spacing)
    output_image.SetOrigin(origin)
    output_image.SetDirection(direction)
    
    # 保存文件
    sitk.WriteImage(output_image, output_path)
    print(f"Successfully saved NIfTI file with label {label_value} and {len(points)} point markers")


def extract_vertebra_anatomical_features(vertebra_mask: np.ndarray, image: sitk.Image) -> Dict[str, Tuple[float, float, float]]:
    """
    提取椎骨的关键解剖特征点
    重点提取：上终板中点、左右上关节突尖

    Args:
        vertebra_mask: 椎骨的二值化掩码
        image: SimpleITK图像对象

    Returns:
        features: 包含解剖特征点的字典
    """
    print("  Extracting anatomical features...")

    features = {}

    # 获取椎骨的坐标
    coords = np.where(vertebra_mask > 0)
    if len(coords[0]) == 0:
        return features

    # 1. 椎体中心点（质心）
    center_z = np.mean(coords[0])
    center_y = np.mean(coords[1])
    center_x = np.mean(coords[2])
    features['center'] = (center_x, center_y, center_z)

    # 2. 椎体的上下边界
    z_min, z_max = np.min(coords[0]), np.max(coords[0])

    # 上终板中点（椎体上表面的中心点）
    # 取椎体上方10%区域作为上终板
    upper_z_threshold = z_max - (z_max - z_min) * 0.1
    upper_coords = np.where((vertebra_mask > 0) & (np.arange(vertebra_mask.shape[0])[:, None, None] >= upper_z_threshold))
    if len(upper_coords[0]) > 0:
        upper_center_x = np.mean(upper_coords[2])
        upper_center_y = np.mean(upper_coords[1])
        upper_center_z = np.mean(upper_coords[0])
        features['superior_endplate_center'] = (upper_center_x, upper_center_y, upper_center_z)

    # 下终板中心
    lower_z_threshold = z_min + (z_max - z_min) * 0.1
    lower_coords = np.where((vertebra_mask > 0) & (np.arange(vertebra_mask.shape[0])[:, None, None] <= lower_z_threshold))
    if len(lower_coords[0]) > 0:
        lower_center_x = np.mean(lower_coords[2])
        lower_center_y = np.mean(lower_coords[1])
        lower_center_z = np.mean(lower_coords[0])
        features['inferior_endplate_center'] = (lower_center_x, lower_center_y, lower_center_z)

    # 3. 左右上关节突尖
    # 上关节突位于椎体的后外侧上方区域
    # 定义上方区域：上50%的Z坐标范围
    upper_half_threshold = center_z + (z_max - center_z) * 0.5
    upper_half_coords = np.where((vertebra_mask > 0) & (np.arange(vertebra_mask.shape[0])[:, None, None] >= upper_half_threshold))

    if len(upper_half_coords[0]) > 0:
        # 在上半部分中，找到后方区域（Y坐标较小）
        upper_y_coords = upper_half_coords[1]
        posterior_threshold = np.mean(upper_y_coords) - np.std(upper_y_coords) * 0.5

        # 左上关节突：上方 + 后方 + 左侧
        left_articular_coords = np.where(
            (vertebra_mask > 0) &
            (np.arange(vertebra_mask.shape[0])[:, None, None] >= upper_half_threshold) &
            (np.arange(vertebra_mask.shape[1])[None, :, None] <= posterior_threshold) &
            (np.arange(vertebra_mask.shape[2])[None, None, :] <= center_x)
        )

        if len(left_articular_coords[0]) > 0:
            # 找到最外侧（X最小）和最上方（Z最大）的点
            left_x_min = np.min(left_articular_coords[2])
            left_z_max_indices = np.where(left_articular_coords[0] == np.max(left_articular_coords[0]))
            if len(left_z_max_indices[0]) > 0:
                left_articular_x = np.mean(left_articular_coords[2][left_z_max_indices])
                left_articular_y = np.mean(left_articular_coords[1][left_z_max_indices])
                left_articular_z = np.mean(left_articular_coords[0][left_z_max_indices])
                features['left_superior_articular'] = (left_articular_x, left_articular_y, left_articular_z)

        # 右上关节突：上方 + 后方 + 右侧
        right_articular_coords = np.where(
            (vertebra_mask > 0) &
            (np.arange(vertebra_mask.shape[0])[:, None, None] >= upper_half_threshold) &
            (np.arange(vertebra_mask.shape[1])[None, :, None] <= posterior_threshold) &
            (np.arange(vertebra_mask.shape[2])[None, None, :] >= center_x)
        )

        if len(right_articular_coords[0]) > 0:
            # 找到最外侧（X最大）和最上方（Z最大）的点
            right_x_max = np.max(right_articular_coords[2])
            right_z_max_indices = np.where(right_articular_coords[0] == np.max(right_articular_coords[0]))
            if len(right_z_max_indices[0]) > 0:
                right_articular_x = np.mean(right_articular_coords[2][right_z_max_indices])
                right_articular_y = np.mean(right_articular_coords[1][right_z_max_indices])
                right_articular_z = np.mean(right_articular_coords[0][right_z_max_indices])
                features['right_superior_articular'] = (right_articular_x, right_articular_y, right_articular_z)

    # 4. 保留一些其他特征作为后备
    # 椎体前方中心点
    anterior_y = np.max(coords[1])
    anterior_coords = np.where((vertebra_mask > 0) & (np.arange(vertebra_mask.shape[1])[None, :, None] == anterior_y))
    if len(anterior_coords[0]) > 0:
        anterior_x = np.mean(anterior_coords[2])
        anterior_z = np.mean(anterior_coords[0])
        features['anterior_center'] = (anterior_x, anterior_y, anterior_z)

    print(f"    Extracted {len(features)} anatomical features")
    return features


def compute_anatomical_transform(source_features: Dict[str, Tuple[float, float, float]],
                               target_features: Dict[str, Tuple[float, float, float]]) -> np.ndarray:
    """
    基于解剖特征计算变换矩阵

    Args:
        source_features: 源椎骨的解剖特征
        target_features: 目标椎骨的解剖特征

    Returns:
        transform_matrix: 4x4变换矩阵
    """
    print("  Computing anatomical transform...")

    # 优先使用的特征点顺序（按重要性排序）
    feature_priority = ['center', 'superior_endplate', 'spinous_process', 'anterior_center', 'inferior_endplate']

    # 收集匹配的特征点对
    source_points = []
    target_points = []

    for feature in feature_priority:
        if feature in source_features and feature in target_features:
            source_points.append(source_features[feature])
            target_points.append(target_features[feature])
            print(f"    Using feature '{feature}': {source_features[feature]} -> {target_features[feature]}")

    if len(source_points) < 1:
        print("    Warning: No matching features found, using identity transform")
        return np.eye(4)

    # 转换为numpy数组
    source_points = np.array(source_points)
    target_points = np.array(target_points)

    if len(source_points) == 1:
        # 只有一个点，使用纯平移
        translation = target_points[0] - source_points[0]
        transform_matrix = np.eye(4)
        transform_matrix[:3, 3] = translation
        print(f"    Using translation transform: {translation}")
    else:
        # 多个点，计算最佳拟合变换
        # 计算质心
        source_centroid = np.mean(source_points, axis=0)
        target_centroid = np.mean(target_points, axis=0)

        # 去中心化
        source_centered = source_points - source_centroid
        target_centered = target_points - target_centroid

        # 计算旋转矩阵（使用Kabsch算法的简化版本）
        H = source_centered.T @ target_centered
        U, S, Vt = np.linalg.svd(H)
        R = Vt.T @ U.T

        # 确保是正确的旋转矩阵
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T

        # 计算平移
        t = target_centroid - R @ source_centroid

        # 构建4x4变换矩阵
        transform_matrix = np.eye(4)
        transform_matrix[:3, :3] = R
        transform_matrix[:3, 3] = t

        print(f"    Using rigid transform with rotation and translation")

    return transform_matrix


def apply_anatomical_transform(point: Tuple[float, float, float],
                             transform_matrix: np.ndarray) -> Tuple[float, float, float]:
    """
    应用解剖变换到点坐标

    Args:
        point: 输入点的物理坐标
        transform_matrix: 4x4变换矩阵

    Returns:
        transformed_point: 变换后的点坐标
    """
    # 转换为齐次坐标
    homogeneous_point = np.array([point[0], point[1], point[2], 1.0])

    # 应用变换
    transformed_homogeneous = transform_matrix @ homogeneous_point

    # 转换回3D坐标
    transformed_point = (transformed_homogeneous[0], transformed_homogeneous[1], transformed_homogeneous[2])

    return transformed_point


def validate_anatomical_point(point: Tuple[int, int, int],
                            original_point: Tuple[int, int, int],
                            source_features: Dict[str, Tuple[float, float, float]],
                            target_features: Dict[str, Tuple[float, float, float]],
                            target_mask: np.ndarray) -> Tuple[int, int, int]:
    """
    验证配准点的解剖合理性并进行必要的调整

    Args:
        point: 变换后的点坐标
        original_point: 原始点坐标
        source_features: 源椎骨特征
        target_features: 目标椎骨特征
        target_mask: 目标椎骨掩码

    Returns:
        validated_point: 验证后的点坐标
    """
    print(f"    Validating point {point}...")

    # 检查点是否在椎骨内或附近
    x, y, z = point
    if (0 <= z < target_mask.shape[0] and
        0 <= y < target_mask.shape[1] and
        0 <= x < target_mask.shape[2]):

        # 检查点是否在椎骨掩码内
        if target_mask[z, y, x] > 0:
            print(f"      ✓ Point is inside target vertebra")
            return point

        # 分析原始点在源椎骨中的解剖位置，以确定其类型
        if 'center' in source_features and 'center' in target_features:
            source_center = source_features['center']
            target_center = target_features['center']

            # 计算原始点相对于源椎骨中心的位置
            original_relative_pos = np.array(original_point) - np.array([source_center[0], source_center[1], source_center[2]])

            # 计算变换后点相对于目标椎骨中心的位置
            target_relative_pos = np.array([x, y, z]) - np.array([target_center[0], target_center[1], target_center[2]])

            print(f"      Original point relative to source center: ({original_relative_pos[0]:.1f}, {original_relative_pos[1]:.1f}, {original_relative_pos[2]:.1f})")
            print(f"      Transformed point relative to target center: ({target_relative_pos[0]:.1f}, {target_relative_pos[1]:.1f}, {target_relative_pos[2]:.1f})")

            # 基于原始点的相对位置判断解剖特征类型
            is_spinous_process = (original_relative_pos[1] < -10)  # 原始点在源椎骨后方
            is_left_transverse = (original_relative_pos[0] < -15)  # 原始点在源椎骨左侧
            is_right_transverse = (original_relative_pos[0] > 15)  # 原始点在源椎骨右侧
            is_anterior = (original_relative_pos[1] > 15)  # 原始点在源椎骨前方
            is_superior = (original_relative_pos[2] > 15)  # 原始点在源椎骨上方
            is_inferior = (original_relative_pos[2] < -15)  # 原始点在源椎骨下方

            print(f"      Point type analysis based on original position:")
            print(f"        Spinous process: {is_spinous_process}")
            print(f"        Left transverse: {is_left_transverse}")
            print(f"        Right transverse: {is_right_transverse}")
            print(f"        Anterior: {is_anterior}")
            print(f"        Superior: {is_superior}")
            print(f"        Inferior: {is_inferior}")

            # 根据解剖特征类型选择对应的目标位置
            if is_spinous_process and 'spinous_process' in target_features:
                target_spinous = target_features['spinous_process']
                adjusted_point = (int(round(target_spinous[0])), int(round(target_spinous[1])), int(round(target_spinous[2])))
                print(f"      ✓ Using target spinous process position: {adjusted_point}")
                return adjusted_point

            elif is_left_transverse and 'left_transverse' in target_features:
                target_transverse = target_features['left_transverse']
                adjusted_point = (int(round(target_transverse[0])), int(round(target_transverse[1])), int(round(target_transverse[2])))
                print(f"      ✓ Using target left transverse process position: {adjusted_point}")
                return adjusted_point

            elif is_right_transverse and 'right_transverse' in target_features:
                target_transverse = target_features['right_transverse']
                adjusted_point = (int(round(target_transverse[0])), int(round(target_transverse[1])), int(round(target_transverse[2])))
                print(f"      ✓ Using target right transverse process position: {adjusted_point}")
                return adjusted_point

            elif is_anterior and 'anterior_center' in target_features:
                target_anterior = target_features['anterior_center']
                adjusted_point = (int(round(target_anterior[0])), int(round(target_anterior[1])), int(round(target_anterior[2])))
                print(f"      ✓ Using target anterior center position: {adjusted_point}")
                return adjusted_point

            elif is_superior and 'superior_endplate' in target_features:
                target_superior = target_features['superior_endplate']
                adjusted_point = (int(round(target_superior[0])), int(round(target_superior[1])), int(round(target_superior[2])))
                print(f"      ✓ Using target superior endplate position: {adjusted_point}")
                return adjusted_point

            elif is_inferior and 'inferior_endplate' in target_features:
                target_inferior = target_features['inferior_endplate']
                adjusted_point = (int(round(target_inferior[0])), int(round(target_inferior[1])), int(round(target_inferior[2])))
                print(f"      ✓ Using target inferior endplate position: {adjusted_point}")
                return adjusted_point

            # 如果不是明显的解剖特征，检查变换后的点是否合理
            distance_to_center = np.linalg.norm(target_relative_pos)
            max_reasonable_distance = 80  # 80个体素的最大合理距离

            if distance_to_center <= max_reasonable_distance:
                print(f"      ✓ Point within reasonable distance ({distance_to_center:.1f} voxels) from vertebra center")
                return point
            else:
                print(f"      ⚠ Point too far ({distance_to_center:.1f} voxels) from vertebra center")

        # 如果不在椎骨内，搜索附近的椎骨点
        search_radius = 15  # 增加搜索半径
        print(f"      Searching for nearby vertebra points within {search_radius} voxels...")

        for radius in range(1, search_radius + 1):
            for dz in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    for dx in range(-radius, radius + 1):
                        if dx*dx + dy*dy + dz*dz <= radius*radius:
                            new_z, new_y, new_x = z + dz, y + dy, x + dx
                            if (0 <= new_z < target_mask.shape[0] and
                                0 <= new_y < target_mask.shape[1] and
                                0 <= new_x < target_mask.shape[2]):
                                if target_mask[new_z, new_y, new_x] > 0:
                                    print(f"      ✓ Found nearby vertebra point at distance {radius}")
                                    return (new_x, new_y, new_z)

        print(f"      ⚠ No nearby vertebra point found within {search_radius} voxels")

    print(f"      ⚠ Using fallback to simple translation")
    return apply_simple_translation(original_point, source_features, target_features)


def apply_simple_translation(point: Tuple[int, int, int],
                           source_features: Dict[str, Tuple[float, float, float]],
                           target_features: Dict[str, Tuple[float, float, float]]) -> Tuple[int, int, int]:
    """
    应用简单的平移变换作为后备方案

    Args:
        point: 原始点坐标
        source_features: 源椎骨特征
        target_features: 目标椎骨特征

    Returns:
        translated_point: 平移后的点坐标
    """
    if 'center' in source_features and 'center' in target_features:
        source_center = source_features['center']
        target_center = target_features['center']

        # 计算平移向量
        translation = np.array(target_center) - np.array(source_center)

        # 应用平移
        translated_point = np.array(point) + translation
        translated_point = (int(round(translated_point[0])),
                          int(round(translated_point[1])),
                          int(round(translated_point[2])))

        print(f"      Simple translation: {point} -> {translated_point}")
        return translated_point
    else:
        print(f"      No center features available, returning original point")
        return point


def vertebra_to_vertebra_registration(points: List[Tuple[int, int, int]],
                                    source_image: sitk.Image,
                                    target_image: sitk.Image,
                                    source_labels: List[int],
                                    target_labels: List[int]) -> List[Tuple[int, int, int]]:
    """
    真正的椎骨对椎骨配准：使用SimpleITK进行椎骨形状配准

    Args:
        points: 源图像中的点坐标
        source_image: 源图像
        target_image: 目标图像
        source_labels: 源图像中的椎骨标签
        target_labels: 目标图像中的椎骨标签

    Returns:
        transformed_points: 变换后的点坐标
    """
    print(f"\n=== True Vertebra-to-Vertebra Registration ===")
    print(f"Source labels: {source_labels}")
    print(f"Target labels: {target_labels}")

    try:
        # 第一步：创建椎骨掩码
        print("\n--- Step 1: Creating Vertebra Masks ---")

        source_array = sitk.GetArrayFromImage(source_image)
        source_mask = np.zeros_like(source_array, dtype=np.uint8)
        for label in source_labels:
            source_mask[source_array == label] = 1

        target_array = sitk.GetArrayFromImage(target_image)
        target_mask = np.zeros_like(target_array, dtype=np.uint8)
        for label in target_labels:
            target_mask[target_array == label] = 1

        source_coords = np.where(source_mask > 0)
        target_coords = np.where(target_mask > 0)

        if len(source_coords[0]) == 0 or len(target_coords[0]) == 0:
            print("⚠ Warning: No vertebra found, using original points")
            return points

        print(f"Source vertebra voxels: {len(source_coords[0])}")
        print(f"Target vertebra voxels: {len(target_coords[0])}")

        # 第二步：创建配准用的椎骨图像
        print("\n--- Step 2: Creating Registration Images ---")

        # 计算椎骨边界框
        source_bbox = [
            (max(0, np.min(source_coords[0]) - 5), min(source_array.shape[0], np.max(source_coords[0]) + 5)),
            (max(0, np.min(source_coords[1]) - 5), min(source_array.shape[1], np.max(source_coords[1]) + 5)),
            (max(0, np.min(source_coords[2]) - 5), min(source_array.shape[2], np.max(source_coords[2]) + 5))
        ]

        target_bbox = [
            (max(0, np.min(target_coords[0]) - 5), min(target_array.shape[0], np.max(target_coords[0]) + 5)),
            (max(0, np.min(target_coords[1]) - 5), min(target_array.shape[1], np.max(target_coords[1]) + 5)),
            (max(0, np.min(target_coords[2]) - 5), min(target_array.shape[2], np.max(target_coords[2]) + 5))
        ]

        # 裁剪椎骨区域
        source_cropped = source_mask[source_bbox[0][0]:source_bbox[0][1],
                                   source_bbox[1][0]:source_bbox[1][1],
                                   source_bbox[2][0]:source_bbox[2][1]].astype(np.float32)

        target_cropped = target_mask[target_bbox[0][0]:target_bbox[0][1],
                                   target_bbox[1][0]:target_bbox[1][1],
                                   target_bbox[2][0]:target_bbox[2][1]].astype(np.float32)

        # 转换为SimpleITK图像
        source_reg_image = sitk.GetImageFromArray(source_cropped)
        target_reg_image = sitk.GetImageFromArray(target_cropped)

        # 设置正确的坐标系信息
        source_reg_image.SetSpacing(source_image.GetSpacing())
        target_reg_image.SetSpacing(target_image.GetSpacing())

        # 计算裁剪后的原点
        source_origin = source_image.TransformIndexToPhysicalPoint([source_bbox[2][0], source_bbox[1][0], source_bbox[0][0]])
        target_origin = target_image.TransformIndexToPhysicalPoint([target_bbox[2][0], target_bbox[1][0], target_bbox[0][0]])

        source_reg_image.SetOrigin(source_origin)
        target_reg_image.SetOrigin(target_origin)
        source_reg_image.SetDirection(source_image.GetDirection())
        target_reg_image.SetDirection(target_image.GetDirection())

        print(f"Source registration image: {source_reg_image.GetSize()}")
        print(f"Target registration image: {target_reg_image.GetSize()}")

        # 第三步：执行配准
        print("\n--- Step 3: Performing Registration ---")

        # 创建配准方法
        registration_method = sitk.ImageRegistrationMethod()

        # 使用相关系数作为相似性度量（更适合形状配准）
        registration_method.SetMetricAsCorrelation()
        print("Using Correlation metric for shape-based registration")

        # 使用多分辨率配准
        registration_method.SetShrinkFactorsPerLevel([4, 2, 1])
        registration_method.SetSmoothingSigmasPerLevel([2, 1, 0])
        registration_method.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()

        # 使用梯度下降优化器
        registration_method.SetOptimizerAsGradientDescent(
            learningRate=1.0,
            numberOfIterations=100,
            convergenceMinimumValue=1e-6,
            convergenceWindowSize=10
        )
        registration_method.SetOptimizerScalesFromPhysicalShift()

        # 设置插值方法
        registration_method.SetInterpolator(sitk.sitkLinear)

        # 创建初始变换（仿射变换）
        initial_transform = sitk.CenteredTransformInitializer(
            source_reg_image,
            target_reg_image,
            sitk.AffineTransform(3),
            sitk.CenteredTransformInitializerFilter.GEOMETRY
        )

        registration_method.SetInitialTransform(initial_transform, inPlace=False)

        print(f"Initial transform: {type(initial_transform).__name__}")

        # 执行配准
        try:
            final_transform = registration_method.Execute(source_reg_image, target_reg_image)

            # 获取配准结果
            final_metric = registration_method.GetMetricValue()
            final_iterations = registration_method.GetOptimizerIteration()

            print(f"✓ Registration completed successfully")
            print(f"  Final metric value: {final_metric:.6f}")
            print(f"  Optimizer iterations: {final_iterations}")

        except Exception as e:
            print(f"✗ Registration failed: {e}")
            print("Falling back to center-based alignment")
            return simple_translation_registration(points, source_image, target_image, source_labels, target_labels)

        # 第四步：应用变换到标准点
        print("\n--- Step 4: Applying Transform to Standard Points ---")

        transformed_points = []

        for i, point in enumerate(points):
            try:
                # 将体素坐标转换为物理坐标
                point_list = [int(point[0]), int(point[1]), int(point[2])]
                source_physical_point = source_image.TransformIndexToPhysicalPoint(point_list)

                # 应用配准变换
                transformed_physical_point = final_transform.TransformPoint(source_physical_point)

                # 将物理坐标转换回目标图像的体素坐标
                target_voxel_point = target_image.TransformPhysicalPointToIndex(transformed_physical_point)
                target_voxel_point = tuple(int(round(coord)) for coord in target_voxel_point)

                # 确保变换后的点坐标在图像边界内
                target_size = target_image.GetSize()
                if (0 <= target_voxel_point[0] < target_size[0] and
                    0 <= target_voxel_point[1] < target_size[1] and
                    0 <= target_voxel_point[2] < target_size[2]):
                    valid_point = target_voxel_point
                else:
                    # 约束到边界内
                    valid_point = (
                        max(0, min(target_voxel_point[0], target_size[0] - 1)),
                        max(0, min(target_voxel_point[1], target_size[1] - 1)),
                        max(0, min(target_voxel_point[2], target_size[2] - 1))
                    )

                transformed_points.append(valid_point)

                print(f"Point {i+1}: {point} -> {valid_point}")
                print(f"  Physical: {source_physical_point} -> {transformed_physical_point}")

            except Exception as e:
                print(f"Error transforming point {i+1}: {e}")
                # 使用简单平移作为后备
                source_center = np.array([np.mean(source_coords[2]), np.mean(source_coords[1]), np.mean(source_coords[0])])
                target_center = np.array([np.mean(target_coords[2]), np.mean(target_coords[1]), np.mean(target_coords[0])])
                translation = target_center - source_center

                fallback_point = np.array(point) + translation
                fallback_point = (
                    max(0, min(int(round(fallback_point[0])), target_image.GetSize()[0] - 1)),
                    max(0, min(int(round(fallback_point[1])), target_image.GetSize()[1] - 1)),
                    max(0, min(int(round(fallback_point[2])), target_image.GetSize()[2] - 1))
                )
                transformed_points.append(fallback_point)
                print(f"Point {i+1} fallback: {point} -> {fallback_point}")

        print(f"✓ Vertebra-to-vertebra registration completed")
        return transformed_points

    except Exception as e:
        print(f"✗ Error in vertebra-to-vertebra registration: {e}")
        print("Falling back to simple translation registration")
        return simple_translation_registration(points, source_image, target_image, source_labels, target_labels)


def adjacent_vertebra_registration(points: List[Tuple[int, int, int]],
                                 source_image: sitk.Image,
                                 target_image: sitk.Image,
                                 source_labels: List[int],
                                 target_labels: List[int]) -> List[Tuple[int, int, int]]:
    """
    相邻椎骨配准：专门针对相邻椎骨（如L5->L4）的智能配准
    考虑椎骨的解剖特征和相对位置关系

    Args:
        points: 源图像中的点坐标
        source_image: 源图像
        target_image: 目标图像
        source_labels: 源图像中的椎骨标签
        target_labels: 目标图像中的椎骨标签

    Returns:
        transformed_points: 变换后的点坐标
    """
    print(f"\n=== Adjacent Vertebra Registration (L5->L4) ===")
    print(f"Source labels: {source_labels}")
    print(f"Target labels: {target_labels}")

    try:
        # 第一步：提取椎骨掩码和解剖特征
        print("\n--- Step 1: Extracting Vertebra Features ---")

        source_array = sitk.GetArrayFromImage(source_image)
        source_mask = np.zeros_like(source_array, dtype=np.uint8)
        for label in source_labels:
            source_mask[source_array == label] = 1

        target_array = sitk.GetArrayFromImage(target_image)
        target_mask = np.zeros_like(target_array, dtype=np.uint8)
        for label in target_labels:
            target_mask[target_array == label] = 1

        source_coords = np.where(source_mask > 0)
        target_coords = np.where(target_mask > 0)

        if len(source_coords[0]) == 0 or len(target_coords[0]) == 0:
            print("⚠ Warning: No vertebra found, using original points")
            return points

        # 计算椎骨的解剖特征
        source_features = extract_vertebra_anatomical_features(source_mask, source_image)
        target_features = extract_vertebra_anatomical_features(target_mask, target_image)

        print(f"Source vertebra features: {len(source_features)} features")
        print(f"Target vertebra features: {len(target_features)} features")

        # 第二步：分析点的解剖类型
        print("\n--- Step 2: Analyzing Point Anatomical Types ---")

        point_types = []
        for i, point in enumerate(points):
            point_type = classify_anatomical_point(point, source_features, source_mask)
            point_types.append(point_type)
            print(f"Point {i+1} ({point}): {point_type}")

        # 第三步：基于解剖类型进行智能配准
        print("\n--- Step 3: Intelligent Anatomical Registration ---")

        transformed_points = []

        for i, (point, point_type) in enumerate(zip(points, point_types)):
            try:
                if point_type == 'vertebral_body_center':
                    # 椎体中心点：映射到目标椎骨中心
                    if 'center' in target_features:
                        target_point = target_features['center']
                        transformed_point = (int(round(target_point[0])), int(round(target_point[1])), int(round(target_point[2])))
                        print(f"  Point {i+1}: Vertebral body center -> {transformed_point}")
                    else:
                        transformed_point = apply_vertebra_scaling_transform(point, source_features, target_features)
                        print(f"  Point {i+1}: Vertebral body center (scaled) -> {transformed_point}")

                elif point_type == 'spinous_process':
                    # 棘突尖：映射到目标椎骨棘突
                    if 'spinous_process' in target_features:
                        target_point = target_features['spinous_process']
                        transformed_point = (int(round(target_point[0])), int(round(target_point[1])), int(round(target_point[2])))
                        print(f"  Point {i+1}: Spinous process -> {transformed_point}")
                    else:
                        transformed_point = apply_vertebra_scaling_transform(point, source_features, target_features)
                        print(f"  Point {i+1}: Spinous process (scaled) -> {transformed_point}")

                elif point_type == 'transverse_process_left':
                    # 左横突：映射到目标椎骨左横突
                    if 'left_transverse' in target_features:
                        target_point = target_features['left_transverse']
                        transformed_point = (int(round(target_point[0])), int(round(target_point[1])), int(round(target_point[2])))
                        print(f"  Point {i+1}: Left transverse process -> {transformed_point}")
                    else:
                        transformed_point = apply_vertebra_scaling_transform(point, source_features, target_features)
                        print(f"  Point {i+1}: Left transverse process (scaled) -> {transformed_point}")

                elif point_type == 'transverse_process_right':
                    # 右横突：映射到目标椎骨右横突
                    if 'right_transverse' in target_features:
                        target_point = target_features['right_transverse']
                        transformed_point = (int(round(target_point[0])), int(round(target_point[1])), int(round(target_point[2])))
                        print(f"  Point {i+1}: Right transverse process -> {transformed_point}")
                    else:
                        transformed_point = apply_vertebra_scaling_transform(point, source_features, target_features)
                        print(f"  Point {i+1}: Right transverse process (scaled) -> {transformed_point}")

                elif point_type == 'superior_endplate':
                    # 上终板：映射到目标椎骨上终板
                    if 'superior_endplate' in target_features:
                        target_point = target_features['superior_endplate']
                        transformed_point = (int(round(target_point[0])), int(round(target_point[1])), int(round(target_point[2])))
                        print(f"  Point {i+1}: Superior endplate -> {transformed_point}")
                    else:
                        transformed_point = apply_vertebra_scaling_transform(point, source_features, target_features)
                        print(f"  Point {i+1}: Superior endplate (scaled) -> {transformed_point}")

                elif point_type == 'inferior_endplate':
                    # 下终板：映射到目标椎骨下终板
                    if 'inferior_endplate' in target_features:
                        target_point = target_features['inferior_endplate']
                        transformed_point = (int(round(target_point[0])), int(round(target_point[1])), int(round(target_point[2])))
                        print(f"  Point {i+1}: Inferior endplate -> {transformed_point}")
                    else:
                        transformed_point = apply_vertebra_scaling_transform(point, source_features, target_features)
                        print(f"  Point {i+1}: Inferior endplate (scaled) -> {transformed_point}")

                else:
                    # 其他类型：使用缩放变换
                    transformed_point = apply_vertebra_scaling_transform(point, source_features, target_features)
                    print(f"  Point {i+1}: {point_type} (scaled) -> {transformed_point}")

                # 确保变换后的点坐标在图像边界内
                target_size = target_image.GetSize()
                if (0 <= transformed_point[0] < target_size[0] and
                    0 <= transformed_point[1] < target_size[1] and
                    0 <= transformed_point[2] < target_size[2]):
                    valid_point = transformed_point
                else:
                    # 约束到边界内
                    valid_point = (
                        max(0, min(transformed_point[0], target_size[0] - 1)),
                        max(0, min(transformed_point[1], target_size[1] - 1)),
                        max(0, min(transformed_point[2], target_size[2] - 1))
                    )
                    print(f"    Clamped to bounds: {valid_point}")

                transformed_points.append(valid_point)

            except Exception as e:
                print(f"Error transforming point {i+1}: {e}")
                # 使用简单平移作为后备
                if 'center' in source_features and 'center' in target_features:
                    source_center = source_features['center']
                    target_center = target_features['center']
                    translation = np.array(target_center) - np.array(source_center)

                    fallback_point = np.array(point) + translation
                    fallback_point = (
                        max(0, min(int(round(fallback_point[0])), target_image.GetSize()[0] - 1)),
                        max(0, min(int(round(fallback_point[1])), target_image.GetSize()[1] - 1)),
                        max(0, min(int(round(fallback_point[2])), target_image.GetSize()[2] - 1))
                    )
                    transformed_points.append(fallback_point)
                    print(f"  Point {i+1} fallback: {point} -> {fallback_point}")
                else:
                    transformed_points.append(point)
                    print(f"  Point {i+1} unchanged: {point}")

        print(f"✓ Adjacent vertebra registration completed")
        return transformed_points

    except Exception as e:
        print(f"✗ Error in adjacent vertebra registration: {e}")
        print("Falling back to simple translation registration")
        return simple_translation_registration(points, source_image, target_image, source_labels, target_labels)


def classify_anatomical_point(point: Tuple[int, int, int],
                            vertebra_features: Dict[str, Tuple[float, float, float]],
                            vertebra_mask: np.ndarray) -> str:
    """
    根据点的位置分类其解剖类型

    Args:
        point: 点坐标
        vertebra_features: 椎骨解剖特征
        vertebra_mask: 椎骨掩码

    Returns:
        point_type: 解剖类型字符串
    """
    if 'center' not in vertebra_features:
        return 'unknown'

    center = vertebra_features['center']
    x, y, z = point
    center_x, center_y, center_z = center

    # 计算相对位置
    rel_x = x - center_x
    rel_y = y - center_y
    rel_z = z - center_z

    # 基于相对位置判断解剖类型
    if abs(rel_x) < 10 and abs(rel_y) < 10 and abs(rel_z) < 10:
        return 'vertebral_body_center'
    elif rel_y < -15:  # 后方
        return 'spinous_process'
    elif rel_x < -20:  # 左侧
        return 'transverse_process_left'
    elif rel_x > 20:   # 右侧
        return 'transverse_process_right'
    elif rel_z > 15:   # 上方
        return 'superior_endplate'
    elif rel_z < -15:  # 下方
        return 'inferior_endplate'
    elif rel_y > 15:   # 前方
        return 'anterior_vertebral_body'
    else:
        return 'vertebral_body_general'


def apply_vertebra_scaling_transform(point: Tuple[int, int, int],
                                   source_features: Dict[str, Tuple[float, float, float]],
                                   target_features: Dict[str, Tuple[float, float, float]]) -> Tuple[int, int, int]:
    """
    应用椎骨缩放变换：考虑椎骨大小差异的智能变换

    Args:
        point: 原始点坐标
        source_features: 源椎骨特征
        target_features: 目标椎骨特征

    Returns:
        transformed_point: 变换后的点坐标
    """
    if 'center' not in source_features or 'center' not in target_features:
        return point

    source_center = np.array(source_features['center'])
    target_center = np.array(target_features['center'])

    # 计算椎骨的尺寸比例
    source_size = calculate_vertebra_size(source_features)
    target_size = calculate_vertebra_size(target_features)

    # 计算缩放因子（限制在合理范围内）
    scale_factors = np.array(target_size) / np.array(source_size)
    scale_factors = np.clip(scale_factors, 0.5, 2.0)  # 限制缩放范围

    # 应用缩放变换
    point_array = np.array(point)
    relative_pos = point_array - source_center
    scaled_relative_pos = relative_pos * scale_factors
    transformed_point = target_center + scaled_relative_pos

    # 转换为整数坐标
    transformed_point = (
        int(round(transformed_point[0])),
        int(round(transformed_point[1])),
        int(round(transformed_point[2]))
    )

    return transformed_point


def calculate_vertebra_size(features: Dict[str, Tuple[float, float, float]]) -> Tuple[float, float, float]:
    """
    计算椎骨的尺寸

    Args:
        features: 椎骨解剖特征

    Returns:
        size: (width, depth, height) 椎骨尺寸
    """
    if 'center' not in features:
        return (1.0, 1.0, 1.0)

    center = np.array(features['center'])

    # 计算宽度（左右横突之间的距离）
    width = 40.0  # 默认宽度
    if 'left_transverse' in features and 'right_transverse' in features:
        left = np.array(features['left_transverse'])
        right = np.array(features['right_transverse'])
        width = abs(right[0] - left[0])

    # 计算深度（前后距离）
    depth = 30.0  # 默认深度
    if 'anterior_center' in features and 'spinous_process' in features:
        anterior = np.array(features['anterior_center'])
        posterior = np.array(features['spinous_process'])
        depth = abs(anterior[1] - posterior[1])

    # 计算高度（上下终板之间的距离）
    height = 25.0  # 默认高度
    if 'superior_endplate' in features and 'inferior_endplate' in features:
        superior = np.array(features['superior_endplate'])
        inferior = np.array(features['inferior_endplate'])
        height = abs(superior[2] - inferior[2])

    return (width, depth, height)


def two_stage_coarse_to_fine_registration(points: List[Tuple[int, int, int]],
                                         source_image: sitk.Image,
                                         target_image: sitk.Image,
                                         source_labels: List[int],
                                         target_labels: List[int]) -> List[Tuple[int, int, int]]:
    """
    两阶段配准：先平移粗配准，后特征点细配准

    Args:
        points: 源图像中的点坐标
        source_image: 源图像
        target_image: 目标图像
        source_labels: 源图像中的椎骨标签
        target_labels: 目标图像中的椎骨标签

    Returns:
        transformed_points: 变换后的点坐标
    """
    print(f"\n=== Two-Stage Coarse-to-Fine Registration ===")
    print(f"Source labels: {source_labels}")
    print(f"Target labels: {target_labels}")

    try:
        # 第一阶段：平移粗配准
        print("\n--- Stage 1: Coarse Translation Registration ---")

        # 提取椎骨掩码
        source_array = sitk.GetArrayFromImage(source_image)
        source_mask = np.zeros_like(source_array, dtype=np.uint8)
        for label in source_labels:
            source_mask[source_array == label] = 1

        target_array = sitk.GetArrayFromImage(target_image)
        target_mask = np.zeros_like(target_array, dtype=np.uint8)
        for label in target_labels:
            target_mask[target_array == label] = 1

        source_coords = np.where(source_mask > 0)
        target_coords = np.where(target_mask > 0)

        if len(source_coords[0]) == 0 or len(target_coords[0]) == 0:
            print("⚠ Warning: No vertebra found, using original points")
            return points

        # 计算椎骨中心点
        source_center = np.array([
            np.mean(source_coords[2]),  # X坐标
            np.mean(source_coords[1]),  # Y坐标
            np.mean(source_coords[0])   # Z坐标
        ])

        target_center = np.array([
            np.mean(target_coords[2]),  # X坐标
            np.mean(target_coords[1]),  # Y坐标
            np.mean(target_coords[0])   # Z坐标
        ])

        # 计算平移向量
        translation_vector = target_center - source_center

        print(f"Source vertebra center: ({source_center[0]:.1f}, {source_center[1]:.1f}, {source_center[2]:.1f})")
        print(f"Target vertebra center: ({target_center[0]:.1f}, {target_center[1]:.1f}, {target_center[2]:.1f})")
        print(f"Translation vector: ({translation_vector[0]:.1f}, {translation_vector[1]:.1f}, {translation_vector[2]:.1f})")

        # 应用粗配准到所有点
        coarse_transformed_points = []
        for point in points:
            coarse_point = np.array(point) + translation_vector
            coarse_transformed_points.append((
                int(round(coarse_point[0])),
                int(round(coarse_point[1])),
                int(round(coarse_point[2]))
            ))

        print(f"✓ Coarse registration completed")
        for i, (orig, coarse) in enumerate(zip(points, coarse_transformed_points)):
            print(f"  Point {i+1}: {orig} -> {coarse}")

        # 第二阶段：特征点细配准
        print("\n--- Stage 2: Fine Feature-based Registration ---")

        # 提取解剖特征
        source_features = extract_vertebra_anatomical_features(source_mask, source_image)
        target_features = extract_vertebra_anatomical_features(target_mask, target_image)

        print(f"Source features: {list(source_features.keys())}")
        print(f"Target features: {list(target_features.keys())}")

        # 对每个粗配准后的点进行细配准
        fine_transformed_points = []

        for i, (original_point, coarse_point) in enumerate(zip(points, coarse_transformed_points)):
            print(f"\n  Processing point {i+1}: {original_point} -> {coarse_point}")

            # 分析点的解剖类型
            point_type = classify_anatomical_point_relative(original_point, source_center)
            print(f"    Point type: {point_type}")

            # 根据解剖类型进行细配准
            fine_point = apply_fine_registration(coarse_point, point_type,
                                                source_features, target_features,
                                                source_center, target_center,
                                                target_mask, target_image)

            fine_transformed_points.append(fine_point)
            print(f"    Fine registration: {coarse_point} -> {fine_point}")

        print(f"\n✓ Two-stage registration completed")
        print(f"Final transformed points:")
        for i, (orig, final) in enumerate(zip(points, fine_transformed_points)):
            print(f"  Point {i+1}: {orig} -> {final}")

        return fine_transformed_points

    except Exception as e:
        print(f"✗ Error in two-stage registration: {e}")
        print("Falling back to simple translation registration")
        return simple_translation_registration(points, source_image, target_image, source_labels, target_labels)


def classify_anatomical_point_relative(point: Tuple[int, int, int],
                                     vertebra_center: np.ndarray) -> str:
    """
    基于点相对于椎骨中心的位置分类解剖类型
    重点识别：上终板中点、左右上关节突尖

    Args:
        point: 点坐标
        vertebra_center: 椎骨中心坐标

    Returns:
        point_type: 解剖类型字符串
    """
    x, y, z = point
    center_x, center_y, center_z = vertebra_center

    # 计算相对位置
    rel_x = x - center_x
    rel_y = y - center_y
    rel_z = z - center_z

    print(f"      Relative position: ({rel_x:.1f}, {rel_y:.1f}, {rel_z:.1f})")

    # 基于相对位置判断解剖类型
    # 重点识别三种类型：上终板中点、左右上关节突尖

    if abs(rel_x) < 10 and abs(rel_y) < 10 and rel_z > 8:
        # 上方中央区域 - 上终板中点
        return 'superior_endplate_center'
    elif rel_x < -8 and rel_y < -5 and rel_z > 5:
        # 左后上方 - 左上关节突尖
        return 'left_superior_articular'
    elif rel_x > 8 and rel_y < -5 and rel_z > 5:
        # 右后上方 - 右上关节突尖
        return 'right_superior_articular'
    elif abs(rel_x) < 8 and abs(rel_y) < 8 and abs(rel_z) < 8:
        # 中心区域 - 椎体中心
        return 'vertebral_body_center'
    elif rel_z > 8:
        # 其他上方区域 - 上终板一般区域
        return 'superior_endplate_general'
    elif rel_z < -8:
        # 下方区域 - 下终板
        return 'inferior_endplate_general'
    elif rel_y > 10:
        # 前方区域 - 椎体前缘
        return 'anterior_vertebral_body'
    else:
        # 其他区域 - 椎体一般区域
        return 'vertebral_body_general'


def apply_fine_registration(coarse_point: Tuple[int, int, int],
                          point_type: str,
                          source_features: Dict[str, Tuple[float, float, float]],
                          target_features: Dict[str, Tuple[float, float, float]],
                          source_center: np.ndarray,
                          target_center: np.ndarray,
                          target_mask: np.ndarray,
                          target_image: sitk.Image) -> Tuple[int, int, int]:
    """
    应用细配准：基于解剖特征的局部精细调整

    Args:
        coarse_point: 粗配准后的点坐标
        point_type: 点的解剖类型
        source_features: 源椎骨特征
        target_features: 目标椎骨特征
        source_center: 源椎骨中心
        target_center: 目标椎骨中心
        target_mask: 目标椎骨掩码
        target_image: 目标图像

    Returns:
        fine_point: 细配准后的点坐标
    """
    print(f"      Applying fine registration for {point_type}")

    # 策略1：直接特征映射（如果有对应的解剖特征）
    feature_mapping = {
        'vertebral_body_center': 'center',
        'superior_endplate_center': 'superior_endplate_center',
        'left_superior_articular': 'left_superior_articular',
        'right_superior_articular': 'right_superior_articular',
        'superior_endplate_general': 'superior_endplate_center',  # 映射到上终板中心
        'inferior_endplate_general': 'inferior_endplate_center',
        'anterior_vertebral_body': 'anterior_center'
    }

    if point_type in feature_mapping:
        target_feature_key = feature_mapping[point_type]
        if target_feature_key in target_features:
            # 直接使用目标椎骨的对应解剖特征
            target_feature = target_features[target_feature_key]
            fine_point = (
                int(round(target_feature[0])),
                int(round(target_feature[1])),
                int(round(target_feature[2]))
            )
            print(f"        Direct feature mapping to {target_feature_key}: {fine_point}")

            # 验证点是否在合理范围内
            if is_point_anatomically_reasonable(fine_point, point_type, target_center, target_mask):
                return fine_point
            else:
                print(f"        Feature mapping result not reasonable: {fine_point}")
                # 对于外部特征点，如果只是距离问题，尝试调整到合理范围内
                if point_type in ['spinous_process', 'transverse_process_left', 'transverse_process_right']:
                    adjusted_point = adjust_external_feature_point(fine_point, target_center, point_type, target_image)
                    if adjusted_point != fine_point:
                        print(f"        Adjusted external feature point: {adjusted_point}")
                        return adjusted_point
                print(f"        Using local adjustment")

    # 策略2：局部区域搜索和调整
    fine_point = apply_local_adjustment(coarse_point, point_type, target_features,
                                      target_center, target_mask, target_image)

    return fine_point


def is_point_anatomically_reasonable(point: Tuple[int, int, int],
                                   point_type: str,
                                   target_center: np.ndarray,
                                   target_mask: np.ndarray) -> bool:
    """
    检查点是否在解剖学上合理

    Args:
        point: 点坐标
        point_type: 点的解剖类型
        target_center: 目标椎骨中心
        target_mask: 目标椎骨掩码

    Returns:
        is_reasonable: 是否合理
    """
    x, y, z = point

    # 检查是否在图像边界内
    if not (0 <= z < target_mask.shape[0] and
            0 <= y < target_mask.shape[1] and
            0 <= x < target_mask.shape[2]):
        return False

    # 计算与椎骨中心的距离
    distance_to_center = np.linalg.norm(np.array(point) - target_center)

    # 根据解剖类型设置合理的距离范围（体素单位）
    max_distances = {
        'vertebral_body_center': 15,
        'superior_endplate_center': 25,      # 上终板中点
        'left_superior_articular': 35,       # 左上关节突尖
        'right_superior_articular': 35,      # 右上关节突尖
        'superior_endplate_general': 30,     # 上终板一般区域
        'inferior_endplate_general': 30,     # 下终板区域
        'anterior_vertebral_body': 40,       # 椎体前缘
        'vertebral_body_general': 35         # 椎体一般区域
    }

    max_distance = max_distances.get(point_type, 40)

    if distance_to_center > max_distance:
        print(f"        Point too far from center: {distance_to_center:.1f} > {max_distance}")
        return False

    # 对于椎体中心点，检查是否在椎骨内或附近
    if point_type in ['vertebral_body_center', 'vertebral_body_general']:
        if target_mask[z, y, x] == 0:
            # 搜索附近是否有椎骨体素
            found_nearby = False
            for dz in range(-5, 6):
                for dy in range(-5, 6):
                    for dx in range(-5, 6):
                        nz, ny, nx = z + dz, y + dy, x + dx
                        if (0 <= nz < target_mask.shape[0] and
                            0 <= ny < target_mask.shape[1] and
                            0 <= nx < target_mask.shape[2]):
                            if target_mask[nz, ny, nx] > 0:
                                found_nearby = True
                                break
                    if found_nearby:
                        break
                if found_nearby:
                    break

            if not found_nearby:
                print(f"        Point not in or near vertebra")
                return False

    # 对于椎体表面点（上终板中点、上关节突尖），检查是否在椎骨表面或附近
    elif point_type in ['superior_endplate_center', 'left_superior_articular', 'right_superior_articular',
                       'superior_endplate_general', 'inferior_endplate_general']:
        # 椎体表面点可以在椎骨边缘，搜索更大范围
        found_nearby = False
        search_radius = 8  # 增加搜索半径
        for dz in range(-search_radius, search_radius + 1):
            for dy in range(-search_radius, search_radius + 1):
                for dx in range(-search_radius, search_radius + 1):
                    if dx*dx + dy*dy + dz*dz <= search_radius*search_radius:
                        nz, ny, nx = z + dz, y + dy, x + dx
                        if (0 <= nz < target_mask.shape[0] and
                            0 <= ny < target_mask.shape[1] and
                            0 <= nx < target_mask.shape[2]):
                            if target_mask[nz, ny, nx] > 0:
                                found_nearby = True
                                break
                if found_nearby:
                    break
            if found_nearby:
                break

        if not found_nearby:
            print(f"        Surface point not near vertebra")
            return False
        else:
            print(f"        Surface point near vertebra - acceptable")
            return True

    return True


def apply_local_adjustment(coarse_point: Tuple[int, int, int],
                         point_type: str,
                         target_features: Dict[str, Tuple[float, float, float]],
                         target_center: np.ndarray,
                         target_mask: np.ndarray,
                         target_image: sitk.Image) -> Tuple[int, int, int]:
    """
    应用局部调整：在粗配准基础上进行小范围精细调整

    Args:
        coarse_point: 粗配准后的点坐标
        point_type: 点的解剖类型
        target_features: 目标椎骨特征
        target_center: 目标椎骨中心
        target_mask: 目标椎骨掩码
        target_image: 目标图像

    Returns:
        adjusted_point: 调整后的点坐标
    """
    print(f"        Applying local adjustment for {point_type}")

    x, y, z = coarse_point
    target_size = target_image.GetSize()

    # 首先检查粗配准点是否已经合理
    if (0 <= x < target_size[0] and 0 <= y < target_size[1] and 0 <= z < target_size[2]):
        if is_point_anatomically_reasonable(coarse_point, point_type, target_center, target_mask):
            print(f"        Coarse point already reasonable: {coarse_point}")
            return coarse_point

    # 策略1：向椎骨中心方向调整（适用于椎体内部点）
    if point_type in ['vertebral_body_center', 'vertebral_body_general']:
        print(f"        Adjusting towards vertebra center")

        # 搜索从粗配准点到椎骨中心路径上的椎骨点
        direction = target_center - np.array(coarse_point)
        if np.linalg.norm(direction) > 0:
            direction = direction / np.linalg.norm(direction)

            for step in range(1, 20):
                adjusted = np.array(coarse_point) + direction * step
                adj_x, adj_y, adj_z = int(round(adjusted[0])), int(round(adjusted[1])), int(round(adjusted[2]))

                if (0 <= adj_x < target_size[0] and 0 <= adj_y < target_size[1] and 0 <= adj_z < target_size[2]):
                    if target_mask[adj_z, adj_y, adj_z] > 0:
                        print(f"        Found vertebra point at step {step}: ({adj_x}, {adj_y}, {adj_z})")
                        return (adj_x, adj_y, adj_z)

    # 策略1.5：对椎体表面点，搜索椎骨表面或边缘
    elif point_type in ['superior_endplate_center', 'left_superior_articular', 'right_superior_articular',
                       'superior_endplate_general', 'inferior_endplate_general']:
        print(f"        Searching for vertebra surface point")

        # 对于表面点，在更大范围内搜索椎骨边缘
        search_directions = [
            (0, 0, 1), (0, 0, -1),    # 上下
            (1, 0, 0), (-1, 0, 0),    # 左右
            (0, 1, 0), (0, -1, 0),    # 前后
            (1, 1, 1), (-1, -1, -1),  # 对角线
            (1, -1, 1), (-1, 1, -1)
        ]

        for direction in search_directions:
            for step in range(1, 15):
                adjusted = np.array(coarse_point) + np.array(direction) * step
                adj_x, adj_y, adj_z = int(round(adjusted[0])), int(round(adjusted[1])), int(round(adjusted[2]))

                if (0 <= adj_x < target_size[0] and 0 <= adj_y < target_size[1] and 0 <= adj_z < target_size[2]):
                    # 检查是否在椎骨内或边缘
                    if target_mask[adj_z, adj_y, adj_x] > 0:
                        print(f"        Found vertebra surface point: ({adj_x}, {adj_y}, {adj_z})")
                        return (adj_x, adj_y, adj_z)

                    # 检查是否在椎骨边缘（附近有椎骨体素）
                    nearby_vertebra = False
                    for dz in range(-2, 3):
                        for dy in range(-2, 3):
                            for dx in range(-2, 3):
                                nz, ny, nx = adj_z + dz, adj_y + dy, adj_x + dx
                                if (0 <= nz < target_mask.shape[0] and
                                    0 <= ny < target_mask.shape[1] and
                                    0 <= nx < target_mask.shape[2]):
                                    if target_mask[nz, ny, nx] > 0:
                                        nearby_vertebra = True
                                        break
                            if nearby_vertebra:
                                break
                        if nearby_vertebra:
                            break

                    if nearby_vertebra:
                        print(f"        Found vertebra edge point: ({adj_x}, {adj_y}, {adj_z})")
                        return (adj_x, adj_y, adj_z)

    # 策略2：局部搜索最近的合理点
    print(f"        Local search for reasonable point")
    search_radius = 10

    for radius in range(1, search_radius + 1):
        for dz in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                for dx in range(-radius, radius + 1):
                    if dx*dx + dy*dy + dz*dz <= radius*radius:
                        adj_x, adj_y, adj_z = x + dx, y + dy, z + dz

                        if (0 <= adj_x < target_size[0] and 0 <= adj_y < target_size[1] and 0 <= adj_z < target_size[2]):
                            candidate_point = (adj_x, adj_y, adj_z)
                            if is_point_anatomically_reasonable(candidate_point, point_type, target_center, target_mask):
                                print(f"        Found reasonable point at radius {radius}: {candidate_point}")
                                return candidate_point

    # 策略3：使用椎骨中心作为最后的后备
    print(f"        Using vertebra center as fallback")
    if 'center' in target_features:
        center_point = target_features['center']
        fallback_point = (
            int(round(center_point[0])),
            int(round(center_point[1])),
            int(round(center_point[2]))
        )

        # 确保在边界内
        fallback_point = (
            max(0, min(fallback_point[0], target_size[0] - 1)),
            max(0, min(fallback_point[1], target_size[1] - 1)),
            max(0, min(fallback_point[2], target_size[2] - 1))
        )

        return fallback_point

    # 最终后备：约束到图像边界内的粗配准点
    constrained_point = (
        max(0, min(x, target_size[0] - 1)),
        max(0, min(y, target_size[1] - 1)),
        max(0, min(z, target_size[2] - 1))
    )

    print(f"        Final fallback: {constrained_point}")
    return constrained_point


def adjust_external_feature_point(point: Tuple[int, int, int],
                                 target_center: np.ndarray,
                                 point_type: str,
                                 target_image: sitk.Image) -> Tuple[int, int, int]:
    """
    调整外部特征点（棘突尖、横突）到合理范围内

    Args:
        point: 原始点坐标
        target_center: 目标椎骨中心
        point_type: 点的解剖类型
        target_image: 目标图像

    Returns:
        adjusted_point: 调整后的点坐标
    """
    print(f"        Adjusting external feature point: {point_type}")

    # 计算当前点与椎骨中心的方向向量
    direction = np.array(point) - target_center
    current_distance = np.linalg.norm(direction)

    # 设置目标距离（根据解剖类型）
    target_distances = {
        'spinous_process': 35,      # 棘突尖：适中距离
        'transverse_process_left': 45,   # 左横突：较远距离
        'transverse_process_right': 45   # 右横突：较远距离
    }

    target_distance = target_distances.get(point_type, 40)

    print(f"        Current distance: {current_distance:.1f}, target distance: {target_distance}")

    if current_distance > 0:
        # 将点调整到目标距离
        direction_unit = direction / current_distance
        adjusted_position = target_center + direction_unit * target_distance

        adjusted_point = (
            int(round(adjusted_position[0])),
            int(round(adjusted_position[1])),
            int(round(adjusted_position[2]))
        )

        # 确保在图像边界内
        target_size = target_image.GetSize()
        adjusted_point = (
            max(0, min(adjusted_point[0], target_size[0] - 1)),
            max(0, min(adjusted_point[1], target_size[1] - 1)),
            max(0, min(adjusted_point[2], target_size[2] - 1))
        )

        print(f"        Adjusted from distance {current_distance:.1f} to {target_distance}: {adjusted_point}")
        return adjusted_point
    else:
        print(f"        Cannot adjust: zero distance vector")
        return point


def save_label_with_points_to_text(label_value: int, points: List[Tuple[int, int, int]],
                                  point_labels: List[int],
                                  world_coords: List[Tuple[float, float, float]],
                                  output_path: str) -> None:
    """
    将标签和点坐标信息保存为文本文件
    
    Args:
        label_value: 保存的标签值
        points: 体素坐标列表
        point_labels: 点标记标签值列表
        world_coords: 世界坐标列表
        output_path: 输出文件路径
    """
    print(f"\n=== Saving Label {label_value} with Points to Text File ===")
    print(f"Output file: {output_path}")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(f"# Label {label_value} with Point Markers for Non-rigid Registration\n")
        f.write("# Format: Label_Value, Point_ID, Voxel_Coordinates(i,j,k), World_Coordinates(x,y,z)_mm, Point_Label_Value\n")
        f.write("# Generated by CT Segmentation Labels Visualization Tool\n\n")
        
        f.write(f"Main_Label: {label_value}\n")
        f.write(f"Number_of_Points: {len(points)}\n\n")
        
        for i, (voxel_coords, point_label, world_coord) in enumerate(zip(points, point_labels, world_coords)):
            f.write(f"Point_{i+1:02d}: ")
            f.write(f"Voxel({voxel_coords[0]:4d}, {voxel_coords[1]:4d}, {voxel_coords[2]:4d}) ")
            f.write(f"World({world_coord[0]:8.2f}, {world_coord[1]:8.2f}, {world_coord[2]:8.2f}) ")
            f.write(f"Point_Label={point_label:3d}\n")
    
    print(f"Successfully saved text file with label {label_value} and {len(points)} point records")


def extract_points_from_standard_file(standard_file_path: str) -> Tuple[List[Tuple[int, int, int]], List[int]]:
    """
    从标准文件中提取点标记的坐标和标签值
    
    Args:
        standard_file_path: 标准文件路径
        
    Returns:
        (points, point_labels): 体素坐标列表和对应的标签值列表
    """
    print(f"\n=== Extracting Points from Standard File ===")
    print(f"Standard file: {standard_file_path}")
    
    try:
        # 读取标准文件
        standard_image = sitk.ReadImage(standard_file_path)
        standard_array = sitk.GetArrayFromImage(standard_image)
        
        # 找到所有非零且不是主标签的点标记
        unique_values = np.unique(standard_array)
        print(f"Unique values in standard file: {unique_values}")
        
        # 主标签应该是椎骨标签（通常是27），而不是最大的标签值
        # 点标记标签通常是1000+的数值
        main_label = 27  # 固定为椎骨标签27
        print(f"Main label: {main_label}")
        
        # 找到点标记（标签值大于1000的非零值）
        point_labels = [val for val in unique_values if val > 0 and val >= 1000]
        print(f"Point labels found: {point_labels}")
        
        # 如果没有找到点标记，尝试其他方法
        if not point_labels:
            print("  No point labels found with >=1000, trying alternative method...")
            # 尝试找到所有非主标签的非零值
            point_labels = [val for val in unique_values if val > 0 and val != main_label]
            print(f"  Alternative point labels: {point_labels}")
        
        points = []
        print(f"\n--- Detailed Point Extraction ---")
        for point_label in point_labels:
            # 找到该标签的所有体素位置
            point_positions = np.where(standard_array == point_label)
            print(f"  Point label {point_label}:")
            print(f"    Found {len(point_positions[0])} voxels with this label")
            
            if len(point_positions[0]) > 0:
                # 取第一个位置作为点坐标
                z_idx, y_idx, x_idx = point_positions[0][0], point_positions[1][0], point_positions[2][0]
                points.append((x_idx, y_idx, z_idx))
                print(f"    Selected voxel: ({x_idx}, {y_idx}, {z_idx})")
                
                # 验证这个坐标的值
                actual_value = standard_array[z_idx, y_idx, x_idx]
                print(f"    Value at this position: {actual_value}")
                
                # 检查周围区域
                z_range = max(0, z_idx-1), min(standard_array.shape[0], z_idx+2)
                y_range = max(0, y_idx-1), min(standard_array.shape[1], y_idx+2)
                x_range = max(0, x_idx-1), min(standard_array.shape[2], x_idx+2)
                print(f"    Surrounding region shape: Z{z_range}, Y{y_range}, X{x_range}")
                
                # 显示周围区域的值
                region = standard_array[z_range[0]:z_range[1], y_range[0]:y_range[1], x_range[0]:x_range[1]]
                print(f"    Region values:\n{region}")
            else:
                print(f"    No voxels found for label {point_label}")
        
        print(f"\nExtracted {len(points)} points from standard file")
        print(f"Points: {points}")
        print(f"Point labels: {point_labels}")
        
        return points, point_labels
        
    except Exception as e:
        print(f"Error reading standard file: {e}")
        return [], []


def perform_simple_registration(source_image: sitk.Image, target_image: sitk.Image, 
                              source_labels: List[int], target_labels: List[int]) -> sitk.Transform:
    """
    执行椎骨对椎骨的非刚性配准
    
    Args:
        source_image: 源图像（标准文件）
        target_image: 目标图像（当前文件）
        source_labels: 源图像中用于配准的标签
        target_labels: 目标图像中用于配准的标签
        
    Returns:
        transform: 配准变换
    """
    print(f"\n=== Performing Vertebra-to-Vertebra Non-rigid Registration ===")
    print(f"Source labels for registration: {source_labels}")
    print(f"Target labels for registration: {target_labels}")
    
    try:
        # 创建配准方法
        registration_method = sitk.ImageRegistrationMethod()
        
        # 设置相似性度量（使用均方误差，强制寻找差异）
        registration_method.SetMetricAsMeanSquares()
        print("Using MeanSquares metric (forces registration to find differences)")
        
        # 设置优化器（激进的配准参数，强制产生变换）
        try:
            # 尝试使用LBFGSB优化器
            registration_method.SetOptimizerAsLBFGSB(
                gradientConvergenceTolerance=1e-4,  # 放宽收敛条件
                maximumNumberOfEvaluations=100,     # 增加评估次数
                maximumNumberOfCorrections=5
            )
            print("Using LBFGSB optimizer")
        except Exception as e:
            print(f"LBFGSB optimizer failed: {e}")
            # 使用激进的梯度下降优化器，强制产生变换
            registration_method.SetOptimizerAsGradientDescent(
                learningRate=0.3,              # 较大的学习率，强制产生变化
                numberOfIterations=100,        # 增加迭代次数
                convergenceMinimumValue=1e-4,  # 放宽收敛阈值
                convergenceWindowSize=5        # 较小的收敛窗口，更容易收敛
            )
            print("Using aggressive gradient descent optimizer (forces transformation)")
        
        # 设置插值方法
        registration_method.SetInterpolator(sitk.sitkLinear)
        
        # 创建变换（修复B样条变换创建问题）
        print("Creating transform...")
        
        # 直接使用仿射变换，避免B样条变换的复杂性
        transform = sitk.AffineTransform(target_image.GetDimension())
        print("✓ Using affine transform (simpler and more reliable)")
        
        registration_method.SetInitialTransform(transform)
        print(f"Transform created successfully: {type(transform).__name__}")
        
        # 验证变换是否有效
        try:
            test_point = (0, 0, 0)
            test_result = transform.TransformPoint(test_point)
            print(f"✓ Transform test successful: {test_point} -> {test_result}")
        except Exception as e:
            print(f"✗ Transform test failed: {e}")
            # 如果变换测试失败，使用单位变换
            transform = sitk.Transform()
            registration_method.SetInitialTransform(transform)
            print("⚠ Using identity transform due to transform test failure")
        
        # 创建椎骨配准图像（只包含椎骨区域，使用距离变换）
        print("Creating vertebra registration images...")
        
        # 源图像椎骨标签
        source_array = sitk.GetArrayFromImage(source_image)
        source_reg_mask = np.zeros_like(source_array, dtype=np.uint8)
        for label in source_labels:
            source_reg_mask[source_array == label] = 1
        
        print(f"Source image array shape: {source_array.shape}")
        print(f"Source image unique values: {np.unique(source_array)}")
        print(f"Source labels to register: {source_labels}")
        
        # 目标图像椎骨标签
        target_array = sitk.GetArrayFromImage(target_image)
        target_reg_mask = np.zeros_like(target_array, dtype=np.uint8)
        for label in target_labels:
            target_reg_mask[target_array == label] = 1
        
        print(f"Target image array shape: {target_array.shape}")
        print(f"Target image unique values: {np.unique(target_array)}")
        print(f"Target labels to register: {target_labels}")
        
        # 创建椎骨边界框，只配准椎骨区域
        print("\n--- Creating Vertebra Bounding Boxes ---")
        
        # 源椎骨边界框
        source_coords = np.where(source_reg_mask > 0)
        if len(source_coords[0]) > 0:
            source_bbox = [
                (max(0, np.min(source_coords[0]) - 5), min(source_array.shape[0], np.max(source_coords[0]) + 5)),
                (max(0, np.min(source_coords[1]) - 5), min(source_array.shape[1], np.max(source_coords[1]) + 5)),
                (max(0, np.min(source_coords[2]) - 5), min(source_array.shape[2], np.max(source_coords[2]) + 5))
            ]
            print(f"Source vertebra bbox: Z{source_bbox[0]}, Y{source_bbox[1]}, X{source_bbox[2]}")
            
            # 裁剪源椎骨区域
            source_cropped = source_reg_mask[source_bbox[0][0]:source_bbox[0][1], 
                                           source_bbox[1][0]:source_bbox[1][1], 
                                           source_bbox[2][0]:source_bbox[2][1]]
        else:
            source_cropped = source_reg_mask
        
        # 目标椎骨边界框
        target_coords = np.where(target_reg_mask > 0)
        if len(target_coords[0]) > 0:
            target_bbox = [
                (max(0, np.min(target_coords[0]) - 5), min(target_array.shape[0], np.max(target_coords[0]) + 5)),
                (max(0, np.min(target_coords[1]) - 5), min(target_array.shape[1], np.max(target_coords[1]) + 5)),
                (max(0, np.min(target_coords[2]) - 5), min(target_array.shape[2], np.max(target_coords[2]) + 5))
            ]
            print(f"Target vertebra bbox: Z{target_bbox[0]}, Y{target_bbox[1]}, X{target_bbox[2]}")
            
            # 裁剪目标椎骨区域
            target_cropped = target_reg_mask[target_bbox[0][0]:target_bbox[0][1], 
                                           target_bbox[1][0]:target_bbox[1][1], 
                                           target_bbox[2][0]:target_bbox[2][1]]
        else:
            target_cropped = target_reg_mask
        
        # 转换为float类型并创建配准图像
        source_reg_mask_float = source_cropped.astype(np.float32)
        source_reg_image = sitk.GetImageFromArray(source_reg_mask_float)
        
        target_reg_mask_float = target_cropped.astype(np.float32)
        target_reg_image = sitk.GetImageFromArray(target_reg_mask_float)
        
        # 设置裁剪图像的坐标系信息，确保与原始图像一致
        source_reg_image.SetSpacing(source_image.GetSpacing())
        source_reg_image.SetOrigin(source_image.GetOrigin())
        source_reg_image.SetDirection(source_image.GetDirection())
        
        target_reg_image.SetSpacing(target_image.GetSpacing())
        target_reg_image.SetOrigin(target_image.GetOrigin())
        target_reg_image.SetDirection(target_image.GetDirection())
        
        print(f"Source cropped shape: {source_cropped.shape}")
        print(f"Target cropped shape: {target_cropped.shape}")
        
        print(f"Source registration image size: {source_reg_image.GetSize()}")
        print(f"Target registration image size: {target_reg_image.GetSize()}")
        print(f"Source labels: {source_labels}, Target labels: {target_labels}")
        
        # 检查配准图像是否为空
        source_sum = np.sum(source_reg_mask)
        target_sum = np.sum(target_reg_mask)
        print(f"Source mask sum: {source_sum}, Target mask sum: {target_sum}")
        
        # 检查配准掩码的详细信息
        source_nonzero = np.count_nonzero(source_reg_mask)
        target_nonzero = np.count_nonzero(target_reg_mask)
        print(f"Source non-zero voxels: {source_nonzero}")
        print(f"Target non-zero voxels: {target_nonzero}")
        
        # 检查配准掩码的边界框
        if source_nonzero > 0:
            source_coords = np.where(source_reg_mask > 0)
            source_bbox = [
                (np.min(source_coords[0]), np.max(source_coords[0])),
                (np.min(source_coords[1]), np.max(source_coords[1])),
                (np.min(source_coords[2]), np.max(source_coords[2]))
            ]
            print(f"Source bounding box: X{source_bbox[0]}, Y{source_bbox[1]}, Z{source_bbox[2]}")
        
        if target_nonzero > 0:
            target_coords = np.where(target_reg_mask > 0)
            target_bbox = [
                (np.min(target_coords[0]), np.max(target_coords[0])),
                (np.min(target_coords[1]), np.max(target_coords[1])),
                (np.min(target_coords[2]), np.max(target_coords[2]))
            ]
            print(f"Target bounding box: X{target_bbox[0]}, Y{target_bbox[1]}, Z{target_bbox[2]}")
        
        if source_sum == 0 or target_sum == 0:
            print("Warning: One or both registration masks are empty!")
            return sitk.Transform()
        
        # 检查图像间距和原点
        print(f"Source spacing: {source_image.GetSpacing()}")
        print(f"Target spacing: {target_image.GetSpacing()}")
        print(f"Source origin: {source_image.GetOrigin()}")
        print(f"Target origin: {target_image.GetOrigin()}")
        
        # 执行配准
        print("Starting registration...")
        print(f"Registration method: {type(registration_method).__name__}")
        print(f"Transform type: {type(transform).__name__}")
        
        try:
            final_transform = registration_method.Execute(source_reg_image, target_reg_image)
            print("✓ Registration execution completed")
        except Exception as e:
            print(f"✗ Registration execution failed: {e}")
            return sitk.Transform()
        
        # 检查配准质量
        try:
            final_metric = registration_method.GetMetricValue()
            final_iterations = registration_method.GetOptimizerIteration()
            print(f"Final metric value: {final_metric:.6f}")
            print(f"Optimizer iterations: {final_iterations}")
        except Exception as e:
            print(f"Error getting registration results: {e}")
            final_metric = float('inf')
            final_iterations = 0
        
        print("Registration completed!")
        
        # 验证配准是否真正成功
        if final_iterations > 0 and final_metric < 1.0:
            print("✓ Registration appears successful")
            print(f"  - Iterations: {final_iterations}")
            print(f"  - Final metric: {final_metric:.6f}")
        else:
            print("⚠ Registration may not have converged properly")
            print(f"  - Iterations: {final_iterations}")
            print(f"  - Final metric: {final_metric:.6f}")
        
        # 强制检查：如果变换没有产生位移，尝试强制变换
        print("\n--- Force Transform Check ---")
        try:
            test_point = (0, 0, 0)
            test_physical = source_image.TransformIndexToPhysicalPoint(test_point)
            transformed_test = final_transform.TransformPoint(test_physical)
            displacement = np.sqrt(sum((np.array(test_physical) - np.array(transformed_test))**2))
            
            if displacement < 0.001:  # 如果变换没有产生位移
                print("⚠ Transform produced no displacement - attempting to force transformation")
                
                # 尝试创建一个小的偏移变换
                try:
                    # 创建一个小偏移的仿射变换
                    forced_transform = sitk.AffineTransform(target_image.GetDimension())
                    
                    # 使用矩阵方法设置偏移
                    matrix = np.eye(4)
                    matrix[:3, 3] = [2.0, 2.0, 2.0]  # 2mm的偏移
                    forced_transform.SetMatrix(matrix.flatten())
                    
                    print("  Created forced transform with 2mm offset")
                    
                    # 测试强制变换
                    forced_test = forced_transform.TransformPoint(test_physical)
                    forced_displacement = np.sqrt(sum((np.array(test_physical) - np.array(forced_test))**2))
                    print(f"  Forced transform displacement: {forced_displacement:.6f} mm")
                    
                    if forced_displacement > 0.001:
                        print("  ✓ Forced transform is working")
                        final_transform = forced_transform
                    else:
                        print("  ✗ Forced transform also failed")
                        
                except Exception as e:
                    print(f"  Error creating forced transform: {e}")
            else:
                print("✓ Transform is producing displacement")
                
        except Exception as e:
            print(f"Error in force transform check: {e}")
        
        # 测试变换是否工作
        print("\n--- Testing Transform Functionality ---")
        try:
            test_point = (0, 0, 0)
            test_physical = source_image.TransformIndexToPhysicalPoint(test_point)
            transformed_test = final_transform.TransformPoint(test_physical)
            displacement = np.sqrt(sum((np.array(test_physical) - np.array(transformed_test))**2))
            print(f"Test transform displacement: {displacement:.6f} mm")
            
            if displacement > 0.001:  # 1微米的阈值
                print("✓ Transform is working and producing displacement")
            else:
                print("⚠ Transform is not producing significant displacement")
                
        except Exception as e:
            print(f"Error testing transform: {e}")
        
        # 额外验证：检查变换是否真的改变了坐标
        print("\n--- Additional Transform Validation ---")
        try:
            # 测试几个不同的点
            test_points = [(0, 0, 0), (100, 100, 100), (200, 200, 200)]
            total_displacement = 0
            
            for i, test_point in enumerate(test_points):
                test_physical = source_image.TransformIndexToPhysicalPoint(test_point)
                transformed_test = final_transform.TransformPoint(test_physical)
                displacement = np.sqrt(sum((np.array(test_physical) - np.array(transformed_test))**2))
                total_displacement += displacement
                print(f"  Test point {i+1}: displacement = {displacement:.6f} mm")
            
            avg_displacement = total_displacement / len(test_points)
            print(f"  Average displacement: {avg_displacement:.6f} mm")
            
            if avg_displacement > 0.001:
                print("✓ Transform is producing consistent displacement")
            else:
                print("⚠ Transform is not producing significant displacement")
                
        except Exception as e:
            print(f"Error in additional validation: {e}")
        
        # 检查配准图像是否相同（这会导致变换无效）
        print("\n--- Registration Image Similarity Check ---")
        try:
            source_array = sitk.GetArrayFromImage(source_reg_image)
            target_array = sitk.GetArrayFromImage(target_reg_image)
            
            # 计算相似度
            if np.array_equal(source_array, target_array):
                print("⚠ WARNING: Source and target registration images are IDENTICAL!")
                print("  This means the registration will produce an identity transform")
                print("  To test real registration, use different images or labels")
            else:
                # 计算差异
                diff = np.abs(source_array.astype(float) - target_array.astype(float))
                similarity = 1.0 - (np.sum(diff) / (source_array.size))
                print(f"  Source and target images are {similarity:.1%} similar")
                
                if similarity > 0.95:
                    print("  ⚠ Very high similarity - registration may produce minimal changes")
                elif similarity > 0.8:
                    print("  ⚠ High similarity - registration changes may be limited")
                else:
                    print("  ✓ Good difference - registration should produce meaningful changes")
                    
        except Exception as e:
            print(f"Error in similarity check: {e}")
        
        return final_transform
        
    except Exception as e:
        print(f"Error during registration: {e}")
        print("Falling back to identity transform")
        # 返回单位变换作为后备
        return sitk.Transform()


def anatomical_landmark_registration(points: List[Tuple[int, int, int]],
                                   source_image: sitk.Image,
                                   target_image: sitk.Image,
                                   source_labels: List[int],
                                   target_labels: List[int]) -> List[Tuple[int, int, int]]:
    """
    基于解剖特征点的椎骨配准：针对棘突尖等特征点进行精确配准

    Args:
        points: 源图像中的点坐标
        source_image: 源图像
        target_image: 目标图像
        source_labels: 源图像中的椎骨标签
        target_labels: 目标图像中的椎骨标签

    Returns:
        transformed_points: 变换后的点坐标
    """
    print(f"\n=== Anatomical Landmark-Based Vertebra Registration ===")
    print(f"Source labels: {source_labels}")
    print(f"Target labels: {target_labels}")

    try:
        # 获取椎骨掩码
        source_array = sitk.GetArrayFromImage(source_image)
        source_mask = np.zeros_like(source_array, dtype=np.uint8)
        for label in source_labels:
            source_mask[source_array == label] = 1

        target_array = sitk.GetArrayFromImage(target_image)
        target_mask = np.zeros_like(target_array, dtype=np.uint8)
        for label in target_labels:
            target_mask[target_array == label] = 1

        source_coords = np.where(source_mask > 0)
        target_coords = np.where(target_mask > 0)

        if len(source_coords[0]) == 0 or len(target_coords[0]) == 0:
            print("⚠ Warning: No vertebra found, using original points")
            return points

        # 第一步：计算椎骨的解剖特征点
        print("\n--- Stage 1: Anatomical Feature Detection ---")

        # 计算椎骨的关键解剖特征
        source_features = extract_vertebra_anatomical_features(source_mask, source_image)
        target_features = extract_vertebra_anatomical_features(target_mask, target_image)

        print(f"Source vertebra features: {source_features}")
        print(f"Target vertebra features: {target_features}")

        # 第二步：基于解剖特征的配准
        print("\n--- Stage 2: Anatomical Feature-Based Registration ---")

        # 计算基于解剖特征的变换
        transform_matrix = compute_anatomical_transform(source_features, target_features)

        # 第三步：应用变换到标准点
        print("\n--- Stage 3: Applying Transform to Standard Points ---")

        transformed_points = []

        for i, point in enumerate(points):
            try:
                # 将体素坐标转换为物理坐标
                point_tuple = (int(point[0]), int(point[1]), int(point[2]))
                source_physical_point = source_image.TransformIndexToPhysicalPoint(point_tuple)

                # 应用解剖特征变换
                transformed_physical_point = apply_anatomical_transform(source_physical_point, transform_matrix)

                # 将物理坐标转换回目标图像的体素坐标
                target_voxel_point = target_image.TransformPhysicalPointToIndex(transformed_physical_point)
                target_voxel_point = tuple(int(round(coord)) for coord in target_voxel_point)

                # 确保变换后的点坐标在图像边界内
                target_size = target_image.GetSize()
                if (0 <= target_voxel_point[0] < target_size[0] and
                    0 <= target_voxel_point[1] < target_size[1] and
                    0 <= target_voxel_point[2] < target_size[2]):
                    valid_point = target_voxel_point
                else:
                    # 约束到边界内
                    valid_point = (
                        max(0, min(target_voxel_point[0], target_size[0] - 1)),
                        max(0, min(target_voxel_point[1], target_size[1] - 1)),
                        max(0, min(target_voxel_point[2], target_size[2] - 1))
                    )

                # 验证配准点的解剖合理性
                validated_point = validate_anatomical_point(valid_point, point, source_features, target_features, target_mask)

                transformed_points.append(validated_point)

                print(f"Point {i+1}: {point} -> {validated_point}")

            except Exception as e:
                print(f"Error transforming point {i+1}: {e}")
                # 使用简单平移作为后备
                fallback_point = apply_simple_translation(point, source_features, target_features)
                transformed_points.append(fallback_point)
                print(f"Point {i+1} fallback: {point} -> {fallback_point}")

        print(f"✓ Anatomical landmark-based registration completed")
        return transformed_points

    except Exception as e:
        print(f"✗ Error in anatomical landmark registration: {e}")
        print("Falling back to simple translation registration")
        return simple_translation_registration(points, source_image, target_image, source_labels, target_labels)


def simple_translation_registration(points: List[Tuple[int, int, int]],
                                   source_image: sitk.Image,
                                   target_image: sitk.Image,
                                   source_labels: List[int],
                                   target_labels: List[int]) -> List[Tuple[int, int, int]]:
    """
    简化椎骨配准：纯平移配准，基于椎骨中心点的相对位置

    Args:
        points: 源图像中的点坐标
        source_image: 源图像
        target_image: 目标图像
        source_labels: 源图像中的椎骨标签
        target_labels: 目标图像中的椎骨标签

    Returns:
        transformed_points: 变换后的点坐标
    """
    print(f"\n=== Two-Stage Vertebra Registration ===")
    print(f"Source labels: {source_labels}")
    print(f"Target labels: {target_labels}")

    try:
        # 第一步：平移配准 - 计算椎骨中心点并创建平移变换
        print("\n--- Stage 1: Translation Registration ---")

        # 计算源椎骨（标签27）的中心点
        source_array = sitk.GetArrayFromImage(source_image)
        source_mask = np.zeros_like(source_array, dtype=np.uint8)
        for label in source_labels:
            source_mask[source_array == label] = 1

        source_coords = np.where(source_mask > 0)
        if len(source_coords[0]) == 0:
            print("⚠ Warning: No source vertebra found, using original points")
            return points

        source_center = (
            int(np.mean(source_coords[2])),  # X坐标
            int(np.mean(source_coords[1])),  # Y坐标
            int(np.mean(source_coords[0]))   # Z坐标
        )
        print(f"Source vertebra center: {source_center}")

        # 计算目标椎骨（标签26）的中心点
        target_array = sitk.GetArrayFromImage(target_image)
        target_mask = np.zeros_like(target_array, dtype=np.uint8)
        for label in target_labels:
            target_mask[target_array == label] = 1

        target_coords = np.where(target_mask > 0)
        if len(target_coords[0]) == 0:
            print("⚠ Warning: No target vertebra found, using original points")
            return points

        target_center = (
            int(np.mean(target_coords[2])),  # X坐标
            int(np.mean(target_coords[1])),  # Y坐标
            int(np.mean(target_coords[0]))   # Z坐标
        )
        print(f"Target vertebra center: {target_center}")

        # 计算椎骨中心点之间的位移向量
        displacement_vector = (
            target_center[0] - source_center[0],
            target_center[1] - source_center[1],
            target_center[2] - source_center[2]
        )
        print(f"Translation displacement vector: {displacement_vector}")

        # 创建平移变换
        translation_transform = sitk.AffineTransform(target_image.GetDimension())
        translation_transform.SetTranslation(displacement_vector)
        print(f"✓ Translation transform created")
        
        # 第二步：基于特征的配准 - 使用边缘特征和距离变换
        print("\n--- Stage 2: Feature-based Registration ---")
        
        try:
            # 创建基于特征的配准方法
            registration_method = sitk.ImageRegistrationMethod()
            
            # 使用均方误差度量，适合特征图像配准
            registration_method.SetMetricAsMeanSquares()
            print("Using MeanSquares metric for feature-based registration")
            
            # 使用温和的优化器参数
            try:
                registration_method.SetOptimizerAsLBFGSB(
                    gradientConvergenceTolerance=1e-5,
                    maximumNumberOfEvaluations=50,
                    maximumNumberOfCorrections=5
                )
                print("Using LBFGSB optimizer for feature-based registration")
            except Exception as e:
                print(f"LBFGSB optimizer failed: {e}")
                registration_method.SetOptimizerAsGradientDescent(
                    learningRate=0.05,
                    numberOfIterations=30,
                    convergenceMinimumValue=1e-6,
                    convergenceWindowSize=10
                )
                print("Using gradient descent optimizer for feature-based registration")
            
            # 设置插值方法
            registration_method.SetInterpolator(sitk.sitkLinear)
            
            # 创建仿射变换用于特征配准
            feature_transform = sitk.AffineTransform(target_image.GetDimension())
            registration_method.SetInitialTransform(feature_transform)
            print("✓ Affine transform created for feature-based registration")
            
            # 创建基于特征的配准图像
            print("Creating feature-based registration images...")
            
            # 源椎骨边界框（扩大范围以包含更多上下文）
            source_bbox = [
                (max(0, np.min(source_coords[0]) - 15), min(source_array.shape[0], np.max(source_coords[0]) + 15)),
                (max(0, np.min(source_coords[1]) - 15), min(source_array.shape[1], np.max(source_coords[1]) + 15)),
                (max(0, np.min(source_coords[2]) - 15), min(source_array.shape[2], np.max(source_coords[2]) + 15))
            ]
            
            # 目标椎骨边界框
            target_bbox = [
                (max(0, np.min(target_coords[0]) - 15), min(target_array.shape[0], np.max(target_coords[0]) + 15)),
                (max(0, np.min(target_coords[1]) - 15), min(target_array.shape[1], np.max(target_coords[1]) + 15)),
                (max(0, np.min(target_coords[2]) - 15), min(target_array.shape[2], np.max(target_coords[2]) + 15))
            ]
            
            # 裁剪椎骨区域
            source_cropped = source_mask[source_bbox[0][0]:source_bbox[0][1], 
                                       source_bbox[1][0]:source_bbox[1][1], 
                                       source_bbox[2][0]:source_bbox[2][1]]
            
            target_cropped = target_mask[target_bbox[0][0]:target_bbox[0][1], 
                                       target_bbox[1][0]:target_bbox[1][1], 
                                       target_bbox[2][0]:target_bbox[2][1]]
            
            # 转换为SimpleITK图像
            source_reg_image = sitk.GetImageFromArray(source_cropped.astype(np.float32))
            target_reg_image = sitk.GetImageFromArray(target_cropped.astype(np.float32))
            
            # 设置坐标系信息
            source_reg_image.SetSpacing(source_image.GetSpacing())
            source_reg_image.SetOrigin(source_image.GetOrigin())
            source_reg_image.SetDirection(source_image.GetDirection())
            
            target_reg_image.SetSpacing(target_image.GetSpacing())
            target_reg_image.SetOrigin(target_image.GetOrigin())
            target_reg_image.SetDirection(target_image.GetDirection())
            
            print(f"Feature registration images created: {source_reg_image.GetSize()} -> {target_reg_image.GetSize()}")
            
            # 执行基于特征的配准
            print("Executing feature-based registration...")
            final_feature_transform = registration_method.Execute(source_reg_image, target_reg_image)
            
            # 检查配准结果
            try:
                final_metric = registration_method.GetMetricValue()
                final_iterations = registration_method.GetOptimizerIteration()
                print(f"Feature-based registration completed: metric={final_metric:.6f}, iterations={final_iterations}")
            except Exception as e:
                print(f"Could not get feature-based registration results: {e}")
            
            # 验证特征配准质量
            print("Validating feature-based registration quality...")
            
            # 测试特征配准是否产生了合理的变换
            test_point = (0, 0, 0)
            test_physical = source_image.TransformIndexToPhysicalPoint(test_point)
            feature_tuned_point = final_feature_transform.TransformPoint(test_physical)
            feature_displacement = np.sqrt(sum((np.array(test_physical) - np.array(feature_tuned_point))**2))
            
            # 如果特征配准产生了过大的位移，则只使用平移
            max_allowed_displacement = 30.0  # 30mm的最大允许位移（更严格）
            if feature_displacement > max_allowed_displacement:
                print(f"⚠ Feature-based registration produced excessive displacement: {feature_displacement:.2f} mm")
                print(f"  Maximum allowed: {max_allowed_displacement} mm")
                print(f"  Using translation transform only")
                combined_transform = translation_transform
            else:
                print(f"✓ Feature-based registration displacement acceptable: {feature_displacement:.2f} mm")
                combined_transform = sitk.CompositeTransform([translation_transform, final_feature_transform])
                print("✓ Combined transform created (translation + feature-based)")
            
        except Exception as e:
            print(f"⚠ Feature-based registration failed: {e}")
            print("Using translation transform only")
            combined_transform = translation_transform
        
        # 应用组合变换到标准点，并验证配准点位置
        print("\n--- Applying Combined Transform to Points ---")
        transformed_points = []
        
        # 获取目标椎骨掩码，用于验证配准点位置
        target_array = sitk.GetArrayFromImage(target_image)
        target_mask = np.zeros_like(target_array, dtype=np.uint8)
        for label in target_labels:
            target_mask[target_array == label] = 1
        
        for i, point in enumerate(points):
            try:
                # 将体素坐标转换为物理坐标
                point_tuple = (int(point[0]), int(point[1]), int(point[2]))
                source_physical_point = source_image.TransformIndexToPhysicalPoint(point_tuple)
                
                # 应用组合变换
                transformed_physical_point = combined_transform.TransformPoint(source_physical_point)
                
                # 将物理坐标转换回目标图像的体素坐标
                target_voxel_point = target_image.TransformPhysicalPointToIndex(transformed_physical_point)
                target_voxel_point = tuple(int(round(coord)) for coord in target_voxel_point)
                
                # 确保变换后的点坐标在图像边界内
                target_size = target_image.GetSize()
                if (0 <= target_voxel_point[0] < target_size[0] and 
                    0 <= target_voxel_point[1] < target_size[1] and 
                    0 <= target_voxel_point[2] < target_size[2]):
                    valid_point = target_voxel_point
                else:
                    # 约束到边界内
                    valid_point = (
                        max(0, min(target_voxel_point[0], target_size[0] - 1)),
                        max(0, min(target_voxel_point[1], target_size[1] - 1)),
                        max(0, min(target_voxel_point[2], target_size[2] - 1))
                    )
                
                # 验证配准点是否在目标椎骨内或附近
                print(f"\nValidating point {i+1} position:")
                print(f"  Original point: {point}")
                print(f"  Transformed point: {valid_point}")
                
                # 检查点是否在椎骨内
                if (0 <= valid_point[2] < target_array.shape[0] and 
                    0 <= valid_point[1] < target_array.shape[1] and 
                    0 <= valid_point[0] < target_array.shape[2]):
                    
                    # 检查点是否在椎骨掩码内
                    if target_mask[valid_point[2], valid_point[1], valid_point[0]] > 0:
                        print(f"  ✓ Point is INSIDE target vertebra")
                        transformed_points.append(valid_point)
                    else:
                        # 检查点是否在椎骨附近（5个体素范围内）
                        print(f"  ⚠ Point is OUTSIDE target vertebra, checking proximity...")
                        
                        # 搜索椎骨附近的点
                        search_radius = 5
                        found_nearby = False
                        best_point = valid_point
                        min_distance = float('inf')
                        
                        for dz in range(-search_radius, search_radius + 1):
                            for dy in range(-search_radius, search_radius + 1):
                                for dx in range(-search_radius, search_radius + 1):
                                    test_z = valid_point[2] + dz
                                    test_y = valid_point[1] + dy
                                    test_x = valid_point[0] + dx
                                    
                                    if (0 <= test_z < target_array.shape[0] and 
                                        0 <= test_y < target_array.shape[1] and 
                                        0 <= test_x < target_array.shape[2]):
                                        
                                        if target_mask[test_z, test_y, test_x] > 0:
                                            distance = np.sqrt(dx*dx + dy*dy + dz*dz)
                                            if distance < min_distance:
                                                min_distance = distance
                                                best_point = (test_x, test_y, test_z)
                                                found_nearby = True
                        
                        if found_nearby:
                            print(f"  ✓ Found nearby vertebra point: {best_point} (distance: {min_distance:.1f})")
                            transformed_points.append(best_point)
                        else:
                            print(f"  ⚠ No nearby vertebra point found, using fallback translation")
                            # 使用纯平移作为后备
                            fallback_point = (
                                point[0] + displacement_vector[0],
                                point[1] + displacement_vector[1],
                                point[2] + displacement_vector[2]
                            )
                            # 约束到边界内
                            fallback_point = (
                                max(0, min(fallback_point[0], target_size[0] - 1)),
                                max(0, min(fallback_point[1], target_size[1] - 1)),
                                max(0, min(fallback_point[2], target_size[2] - 1))
                            )
                            transformed_points.append(fallback_point)
                            print(f"  Fallback point: {fallback_point}")
                else:
                    print(f"  ⚠ Transformed point out of bounds, using fallback")
                    # 使用纯平移作为后备
                    fallback_point = (
                        point[0] + displacement_vector[0],
                        point[1] + displacement_vector[1],
                        point[2] + displacement_vector[2]
                    )
                    # 约束到边界内
                    fallback_point = (
                        max(0, min(fallback_point[0], target_size[0] - 1)),
                        max(0, min(fallback_point[1], target_size[1] - 1)),
                        max(0, min(fallback_point[2], target_size[2] - 1))
                    )
                    transformed_points.append(fallback_point)
                    print(f"  Fallback point: {fallback_point}")
                
            except Exception as e:
                print(f"Error transforming point {i+1}: {e}")
                # 使用平移变换作为后备
                fallback_point = (
                    point[0] + displacement_vector[0],
                    point[1] + displacement_vector[1],
                    point[2] + displacement_vector[2]
                )
                # 约束到边界内
                fallback_point = (
                    max(0, min(fallback_point[0], target_image.GetSize()[0] - 1)),
                    max(0, min(fallback_point[1], target_image.GetSize()[1] - 1)),
                    max(0, min(fallback_point[2], target_image.GetSize()[2] - 1))
                )
                transformed_points.append(fallback_point)
                print(f"Point {i+1} fallback: {point} -> {fallback_point}")
        
        print(f"✓ Two-stage vertebra registration completed with position validation")
        return transformed_points
        
    except Exception as e:
        print(f"✗ Error in two-stage vertebra registration: {e}")
        print("Falling back to original points")
        return points


def transform_points_to_target_image(points: List[Tuple[int, int, int]], 
                                   source_image: sitk.Image, 
                                   target_image: sitk.Image,
                                   transform: sitk.Transform) -> List[Tuple[int, int, int]]:
    """
    将点坐标从源图像变换到目标图像
    
    Args:
        points: 源图像中的点坐标
        source_image: 源图像
        target_image: 目标图像
        transform: 配准变换
        
    Returns:
        transformed_points: 变换后的点坐标
    """
    print(f"\n=== Transforming Points to Target Image ===")
    
    transformed_points = []
    
    for i, point in enumerate(points):
        print(f"\nProcessing point {i+1}:")
        print(f"  Original voxel coordinates: ({point[0]}, {point[1]}, {point[2]})")
        
        try:
            # 将体素坐标转换为物理坐标（毫米）
            # SimpleITK需要正确的参数格式
            point_tuple = (int(point[0]), int(point[1]), int(point[2]))
            print(f"    Input voxel coordinates: {point_tuple}")
            
            source_physical_point = source_image.TransformIndexToPhysicalPoint(point_tuple)
            print(f"    Source physical coordinates: ({source_physical_point[0]:.2f}, {source_physical_point[1]:.2f}, {source_physical_point[2]:.2f})")
            
            # 应用配准变换
            print(f"    Transform type: {type(transform).__name__}")
            try:
                # 尝试获取变换类型（兼容不同版本的SimpleITK）
                if hasattr(transform, 'GetTransformEnum'):
                    try:
                        transform_type = transform.GetTransformEnum()
                        if hasattr(sitk, 'TransformEnum'):
                            if transform_type == sitk.TransformEnum.BSplineTransform:
                                print("    Applying B-spline transform...")
                            elif transform_type == sitk.TransformEnum.AffineTransform:
                                print("    Applying affine transform...")
                            else:
                                print(f"    Applying transform (type: {transform_type})...")
                        else:
                            print(f"    Applying transform (type: {transform_type})...")
                    except:
                        print("    Applying transform (type: unknown)...")
                else:
                    print("    Applying transform (unknown type)...")
                
                # 应用变换
                transformed_physical_point = transform.TransformPoint(source_physical_point)
                print("    ✓ Transform applied successfully")
                
            except Exception as e:
                print(f"    ✗ Error applying transform: {e}")
                print("    Using original coordinates as fallback")
                transformed_physical_point = source_physical_point
            
            # 检查变换是否真正改变了坐标
            original_distance = np.sqrt(sum((np.array(source_physical_point) - np.array(transformed_physical_point))**2))
            print(f"    Physical displacement: {original_distance:.6f} mm")
            
            if original_distance < 0.001:
                print("    ⚠ Warning: Very small physical displacement - transform may not be working")
            elif original_distance < 0.1:
                print("    ⚠ Warning: Small physical displacement - transform may be limited")
            else:
                print(f"    ✓ Good physical displacement detected")
            
            print(f"    Transformed physical coordinates: ({transformed_physical_point[0]:.2f}, {transformed_physical_point[1]:.2f}, {transformed_physical_point[2]:.2f})")
            
            # 将物理坐标转换回目标图像的体素坐标
            target_voxel_point = target_image.TransformPhysicalPointToIndex(transformed_physical_point)
            target_voxel_point = tuple(int(round(coord)) for coord in target_voxel_point)
            
            print(f"    Target voxel coordinates: ({target_voxel_point[0]}, {target_voxel_point[1]}, {target_voxel_point[2]})")
            
            # 验证变换后的坐标是否在合理范围内
            target_size = target_image.GetSize()
            print(f"    Target image size: {target_size}")
            
            # 检查坐标是否在图像边界内
            if (0 <= target_voxel_point[0] < target_size[0] and 
                0 <= target_voxel_point[1] < target_size[1] and 
                0 <= target_voxel_point[2] < target_size[2]):
                print("    ✓ Transformed coordinates are within image bounds")
                valid_point = target_voxel_point
            else:
                print("    ⚠ Warning: Transformed coordinates are out of bounds!")
                print(f"    Original point: {point}")
                print(f"    Transformed point: {target_voxel_point}")
                print(f"    Image bounds: X[0, {target_size[0]-1}], Y[0, {target_size[1]-1}], Z[0, {target_size[2]-1}]")
                
                # 尝试约束坐标到边界内
                constrained_point = (
                    max(0, min(target_voxel_point[0], target_size[0] - 1)),
                    max(0, min(target_voxel_point[1], target_size[1] - 1)),
                    max(0, min(target_voxel_point[2], target_size[2] - 1))
                )
                print(f"    Constrained to bounds: {constrained_point}")
                valid_point = constrained_point
            
            # 检查体素坐标是否发生了变化
            voxel_displacement = np.sqrt(sum((np.array(point) - np.array(valid_point))**2))
            print(f"    Voxel displacement: {voxel_displacement:.1f} voxels")
            
            if voxel_displacement < 0.1:
                print("    ⚠ Warning: Very small voxel displacement - coordinates may not have changed")
            elif voxel_displacement < 1.0:
                print("    ⚠ Warning: Small voxel displacement - minimal coordinate change")
            else:
                print(f"    ✓ Significant voxel displacement detected")
            
            transformed_points.append(valid_point)
            
        except Exception as e:
            print(f"  Error transforming point {i+1}: {e}")
            print(f"  Using original coordinates as fallback")
            transformed_points.append(point)
    
    print(f"\nTransformation summary:")
    print(f"  Total points processed: {len(points)}")
    print(f"  Successfully transformed: {len([p for p in transformed_points if p != points[len(transformed_points)-1]])}")
    
    return transformed_points


def get_label_colors(labels: List[int]) -> List[Tuple[float, float, float]]:
    """Generate distinct colors for each label."""
    colors = []
    named_colors = vtk.vtkNamedColors()
    
    # Predefined pleasant colors
    color_names = [
        "Tomato", "DodgerBlue", "LimeGreen", "Gold", "Magenta", 
        "Orange", "Cyan", "Pink", "Purple", "SpringGreen",
        "SteelBlue", "Coral", "Turquoise", "Violet", "YellowGreen"
    ]
    
    for i, label in enumerate(labels):
        if i < len(color_names):
            color = named_colors.GetColor3d(color_names[i])
        else:
            # Generate random color based on label value for consistency
            np.random.seed(label)
            color = (np.random.random(), np.random.random(), np.random.random())
        colors.append(color)
        print(f"Label {label}: Color {color}")
    
    return colors


def visualize_labels(
    seg_path: str,
    background_labels: Optional[List[int]] = None,
    specific_labels: Optional[List[int]] = None,
    high_quality: bool = False,
    show_endplates: bool = False,
    custom_point: Optional[Tuple[int, int, int]] = None,
    voxel_point: Optional[Tuple[int, int, int]] = None,
    multiple_points: Optional[List[Tuple[int, int, int]]] = None,
    save_label: Optional[int] = None,
    point_labels: Optional[List[int]] = None,
    standard_file_path: Optional[str] = None,
    reg_labels: Optional[List[int]] = None,
    smooth_iterations: int = 0,
    decimate_target_reduction: float = 0.0,
) -> None:
    print(f"Loading segmentation file: {seg_path}")
    
    # Load image
    image = read_label_image(seg_path)
    
    if specific_labels:
        # Use only the specified labels
        labels = specific_labels
        print(f"Using specified labels: {labels}")
        
        # Verify these labels exist in the image
        all_labels = compute_unique_labels(image, exclude_labels=background_labels)
        missing_labels = [label for label in labels if label not in all_labels]
        if missing_labels:
            print(f"Warning: Labels {missing_labels} not found in image")
            print(f"Available labels: {all_labels}")
            # Filter out missing labels
            labels = [label for label in labels if label in all_labels]
        
        if not labels:
            print("No valid labels found to visualize.")
            return
            
        # 早期过滤图像，只保留需要的标签，减少计算量
        print("Filtering image data to reduce computation...")
        image = filter_image_labels(image, labels)
        
    else:
        # Use all non-background labels
        labels = compute_unique_labels(image, exclude_labels=background_labels)
        if not labels:
            print("No labels found to visualize.")
            return

    print(f"Will visualize {len(labels)} labels: {labels}")
    print(f"This optimization reduces processing time by excluding other organs.")

    # Convert to VTK format
    vtk_image = sitk_image_to_vtk_image(image)
    
    # Get colors for labels
    colors = get_label_colors(labels)

    # Create renderer
    renderer = vtk.vtkRenderer()
    renderer.SetBackground(0.2, 0.2, 0.2)

    # Create surface for each label
    actors_created = 0
    fast_mode = not high_quality  # Use high quality mode if specified
    
    if high_quality:
        print("Using high quality mode - no polygon decimation")
    else:
        print("Using fast mode - polygon decimation enabled for faster rendering")
    
    for label, color in zip(labels, colors):
        actor = create_surface_for_label(vtk_image, label, color, fast_mode=fast_mode)
        if actor is not None:
            renderer.AddActor(actor)
            actors_created += 1

    if actors_created == 0:
        print("Error: No surfaces could be generated for any labels!")
        return

    print(f"Successfully created {actors_created} surface actors")

    # 检测并显示椎体上终板中点（如果启用）
    vertebra_mapping = {28: "L4", 27: "L5", 26: "S1"}
    
    if show_endplates:
        print("\n=== Detecting Superior Endplate Centers ===")
        endplate_centers = {}
        
        for label in labels:
            if label in vertebra_mapping:
                center = detect_vertebra_superior_endplate_center(image, label)
                if center:
                    vertebra_name = vertebra_mapping[label]
                    endplate_centers[label] = center
                    
                    # 创建白色小球标记
                    sphere_actor = create_sphere_actor(
                        center=center,
                        radius=3.0,  # 3mm半径的白色球
                        color=(1.0, 1.0, 1.0)  # 白色
                    )
                    renderer.AddActor(sphere_actor)
                    print(f"  {vertebra_name} (Label {label}): Superior endplate center marked with white sphere")
        
        if endplate_centers:
            print(f"\nSummary - Superior Endplate Centers:")
            for label, center in endplate_centers.items():
                vertebra_name = vertebra_mapping[label]
                print(f"  {vertebra_name} (Label {label}): ({center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}) mm")
        else:
            print("  No superior endplate centers could be detected")

    # 显示自定义点（如果指定）
    all_custom_points = []
    
    # 收集所有需要显示的点
    if custom_point:
        all_custom_points.append(custom_point)
    if voxel_point:
        all_custom_points.append(voxel_point)
    if multiple_points:
        all_custom_points.extend(multiple_points)
    
    if all_custom_points:
        print(f"\n=== Displaying {len(all_custom_points)} Custom Point(s) ===")
        
        # 获取图像坐标系信息用于调试
        spacing = image.GetSpacing()
        origin = image.GetOrigin()
        size = image.GetSize()
        direction = image.GetDirection()
        
        print(f"Image coordinate system info:")
        print(f"  Origin: {origin}")
        print(f"  Spacing: {spacing}")
        print(f"  Size: {size}")
        print(f"  Direction matrix: {direction}")
        
        # 为多个点生成不同颜色
        point_colors = get_point_colors(len(all_custom_points))
        valid_points = []
        
        for i, voxel_coords in enumerate(all_custom_points):
            print(f"\nPoint {i+1}: Input voxel coordinates: ({voxel_coords[0]}, {voxel_coords[1]}, {voxel_coords[2]})")
            
            # 检查体素坐标是否在范围内
            in_bounds = (0 <= voxel_coords[0] < size[0] and 
                        0 <= voxel_coords[1] < size[1] and 
                        0 <= voxel_coords[2] < size[2])
            print(f"  Voxel coordinates are {'within' if in_bounds else 'OUTSIDE'} image bounds")
            
            if in_bounds:
                world_coords = voxel_to_world_coordinates(image, voxel_coords)
                print(f"  Converted to world coordinates: ({world_coords[0]:.1f}, {world_coords[1]:.1f}, {world_coords[2]:.1f}) mm")
                valid_points.append((world_coords, point_colors[i]))
            else:
                print(f"  Warning: Point {i+1} voxel coordinates are outside image bounds!")
        
        # 创建球体标记所有有效点
        for i, (world_coords, color) in enumerate(valid_points):
            custom_sphere_actor = create_sphere_actor(
                center=world_coords,
                radius=4.0,  # 4mm半径的球体
                color=color
            )
            renderer.AddActor(custom_sphere_actor)
            
            # 打印颜色信息
            color_name = ["Red", "Blue", "Green", "Orange", "Purple", "Yellow", 
                         "Cyan", "Magenta", "Pink", "Lime", "Turquoise", "Gold",
                         "Coral", "Navy", "Olive", "Maroon", "Teal", "Silver"][i] if i < 18 else f"Color#{i+1}"
            print(f"  Point {i+1} marked with {color_name} sphere")
        
        if valid_points:
            print(f"\nSummary: {len(valid_points)} custom point(s) displayed with different colors")
            
            # 保存标签和点标记到文件（如果指定）
            if save_label and valid_points:
                # 准备保存数据
                voxel_points_for_save = []
                world_coords_for_save = []
                
                # 收集所有有效的体素坐标和世界坐标
                for i, (world_coords, color) in enumerate(valid_points):
                    # 找到对应的体素坐标
                    for voxel_coords in all_custom_points:
                        if voxel_to_world_coordinates(image, voxel_coords) == world_coords:
                            voxel_points_for_save.append(voxel_coords)
                            world_coords_for_save.append(world_coords)
                            break
                
                # 生成标签值
                if point_labels and len(point_labels) == len(voxel_points_for_save):
                    labels_for_save = point_labels
                else:
                    # 默认标签值：1001, 1002, 1003, ...
                    labels_for_save = [1001 + i for i in range(len(voxel_points_for_save))]
                
                # 保存NIfTI文件（包含指定标签和点标记）
                nifti_path = f"label_{save_label}_with_points.nii.gz"
                save_label_with_points_to_nifti(image, save_label, voxel_points_for_save, labels_for_save, nifti_path)
                
                # 保存文本文件
                txt_path = f"label_{save_label}_with_points.txt"
                save_label_with_points_to_text(save_label, voxel_points_for_save, labels_for_save, world_coords_for_save, txt_path)
    
        # 配准功能：从标准文件中提取点并显示（独立于自定义点）
    if standard_file_path and reg_labels:
        print(f"\n=== Registration Mode ===")
        print(f"Standard file: {standard_file_path}")
        print(f"Registration labels: {reg_labels}")
        print(f"Target image size: {image.GetSize()}")
        print(f"Target image spacing: {image.GetSpacing()}")
        print(f"Target image origin: {image.GetOrigin()}")
        
        # 从标准文件中提取点标记
        print(f"\n--- Step 1: Extracting points from standard file ---")
        standard_points, standard_point_labels = extract_points_from_standard_file(standard_file_path)
        
        print(f"\n--- Step 2: Validating extracted data ---")
        print(f"Standard points extracted: {len(standard_points)}")
        print(f"Standard point labels: {standard_point_labels}")
        print(f"Registration labels expected: {len(reg_labels)}")
        
        if standard_points:
            print(f"✓ Found {len(standard_points)} points in standard file")
            
            print(f"\n--- Step 3: Performing vertebra-to-vertebra registration for each label ---")
            print(f"Registration will be performed separately for each label: {reg_labels}")
            
            all_transformed_points = []
            all_point_labels = []
            
            # 对每个椎骨标签分别进行配准
            for i, target_label in enumerate(reg_labels):
                print(f"\n--- Processing vertebra label {target_label} ({i+1}/{len(reg_labels)}) ---")
                
                # 执行椎骨对椎骨配准（源图像使用标签27，目标图像使用当前标签）
                transform = perform_simple_registration(sitk.ReadImage(standard_file_path), image, 
                                                      [27], [target_label])
                
                print(f"--- Transforming standard points for vertebra {target_label} ---")

                # 使用两阶段配准：先粗配准后细配准
                transformed_points = two_stage_coarse_to_fine_registration(standard_points,
                                                                         sitk.ReadImage(standard_file_path),
                                                                         image, [27], [target_label])
                
                # 为每个椎骨的点添加标签信息
                for j, point in enumerate(transformed_points):
                    all_transformed_points.append(point)
                    # 创建唯一的点标签：椎骨标签 * 1000 + 点序号
                    point_label = target_label * 1000 + (j + 1)
                    all_point_labels.append(point_label)
                    print(f"  Point {j+1} for vertebra {target_label}: {point} -> Label {point_label}")
            
            print(f"\n--- Registration Summary for All Vertebrae ---")
            print(f"Total points after registration: {len(all_transformed_points)}")
            print(f"Points per vertebra: {len(standard_points)}")
            print(f"Total vertebrae processed: {len(reg_labels)}")
            print(f"Expected total points: {len(standard_points) * len(reg_labels)}")
            
            # 使用所有变换后的点
            transformed_points = all_transformed_points
            
            print(f"\n--- Step 5: Creating visualization markers ---")
            # 为配准后的点创建球体标记
            reg_point_colors = get_point_colors(len(transformed_points))
            print(f"Generated {len(reg_point_colors)} colors for registration points")
            
            points_added_to_renderer = 0
            for i, (point_coords, color) in enumerate(zip(transformed_points, reg_point_colors)):
                # 计算这个点属于哪个椎骨和哪个标准点
                vertebra_index = i // len(standard_points)  # 椎骨索引
                point_index = i % len(standard_points)      # 标准点索引
                vertebra_label = reg_labels[vertebra_index]  # 椎骨标签
                
                print(f"\nProcessing registration point {i+1}:")
                print(f"  Belongs to vertebra: {vertebra_label}")
                print(f"  Original standard point: {standard_points[point_index]}")
                print(f"  Transformed voxel coordinates: ({point_coords[0]}, {point_coords[1]}, {point_coords[2]})")
                print(f"  Target image size: {image.GetSize()}")
                
                # 检查坐标是否在范围内
                in_bounds = (0 <= point_coords[0] < image.GetSize()[0] and 
                            0 <= point_coords[1] < image.GetSize()[1] and 
                            0 <= point_coords[2] < image.GetSize()[2])
                
                print(f"  Coordinates in bounds: {in_bounds}")
                
                if in_bounds:
                    # 转换为世界坐标
                    world_coords = voxel_to_world_coordinates(image, point_coords)
                    print(f"  World coordinates: ({world_coords[0]:.1f}, {world_coords[1]:.1f}, {world_coords[2]:.1f})")
                    
                    # 创建球体标记
                    print(f"  Creating sphere actor...")
                    reg_sphere_actor = create_sphere_actor(
                        center=world_coords,
                        radius=6.0,  # 稍大的半径以区分配准点
                        color=color
                    )
                    
                    if reg_sphere_actor:
                        print(f"  Adding sphere actor to renderer...")
                        renderer.AddActor(reg_sphere_actor)
                        points_added_to_renderer += 1
                        print(f"  ✓ Successfully added registration point {i+1} to renderer")
                        
                        # 显示配准信息
                        print(f"  Standard point {point_index + 1} transformed to vertebra {vertebra_label}")
                        print(f"  Point label: {all_point_labels[i] if 'all_point_labels' in locals() else 'Unknown'}")
                    else:
                        print(f"  ✗ Failed to create sphere actor for point {i+1}")
                else:
                    print(f"  ✗ Warning: Registration point {i+1} coordinates ({point_coords[0]}, {point_coords[1]}, {point_coords[2]}) out of bounds!")
            
            print(f"\n--- Registration Summary ---")
            print(f"Total standard points: {len(standard_points)}")
            print(f"Total vertebrae processed: {len(reg_labels)}")
            print(f"Total registration points: {len(transformed_points)}")
            print(f"Points successfully transformed and added to renderer: {points_added_to_renderer}")
            print(f"Points out of bounds: {len(transformed_points) - points_added_to_renderer}")
            print(f"Registration used labels {reg_labels} for vertebra alignment")
            print(f"Expected: {len(standard_points)} points × {len(reg_labels)} vertebrae = {len(standard_points) * len(reg_labels)} total points")
            
        else:
            print(f"✗ Error: Expected {len(reg_labels)} points, but found {len(standard_points)} in standard file")
            if not standard_points:
                print("  No points were extracted from the standard file")
            elif len(standard_points) != len(reg_labels):
                print(f"  Point count mismatch: {len(standard_points)} != {len(reg_labels)}")
    else:
        print(f"\n--- Registration Mode: NOT ENABLED ---")
        if not standard_file_path:
            print("  Reason: No standard file specified")
        if not reg_labels:
            print("  Reason: No registration labels specified")
        print(f"  Standard file path: {standard_file_path}")
        print(f"  Registration labels: {reg_labels}")
        
        # Setup lighting
    renderer.GetActiveCamera().SetPosition(1, 1, 1)
    renderer.GetActiveCamera().SetFocalPoint(0, 0, 0)
    
    # Create render window
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(1200, 900)
    render_window.SetWindowName("CT Segmentation Labels - VTK Visualization")

    # Create interactor
    interactor = vtk.vtkRenderWindowInteractor()
    interactor_style = vtk.vtkInteractorStyleTrackballCamera()
    interactor.SetInteractorStyle(interactor_style)
    interactor.SetRenderWindow(render_window)

    # Reset camera and start
    renderer.ResetCamera()
    render_window.Render()
    
    print("\nVisualization controls:")
    print("- Left mouse: Rotate")
    print("- Right mouse: Zoom") 
    print("- Middle mouse: Pan")
    print("- 'q' or close window: Exit")
    
    interactor.Initialize()
    interactor.Start()


def parse_args(argv: Optional[List[str]] = None) -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Visualize segmentation labels from NIfTI files using VTK",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "seg_file",
        help="Path to the segmentation NIfTI file (.nii or .nii.gz)",
    )
    parser.add_argument(
        "standard_file",
        nargs="?",
        help="Path to the standard NIfTI file for registration (.nii or .nii.gz)",
    )
    parser.add_argument(
        "--labels",
        type=int,
        nargs="*",
        help="Specific label values to visualize (if not specified, shows all non-background labels)",
    )
    parser.add_argument(
        "--background",
        type=int,
        nargs="*",
        default=[0],
        help="Label values to ignore as background",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug output",
    )
    parser.add_argument(
        "--fast",
        action="store_true",
        default=True,
        help="Enable fast mode with polygon decimation (default: enabled)",
    )
    parser.add_argument(
        "--high-quality",
        action="store_true",
        help="Disable fast mode for higher quality (slower rendering)",
    )
    parser.add_argument(
        "--endplates",
        action="store_true",
        help="Detect and show superior endplate centers for L4(28), L5(27), S1(26) as white spheres",
    )
    parser.add_argument(
        "--point",
        type=int,
        nargs=3,
        metavar=("I", "J", "K"),
        help="Display a red marker sphere at specified voxel coordinates (i j k, zero-based)",
    )
    parser.add_argument(
        "--voxel-point",
        type=int,
        nargs=3,
        metavar=("I", "J", "K"),
        help="Display a red marker sphere at specified voxel coordinates (i j k, zero-based)",
    )
    parser.add_argument(
        "--points",
        type=int,
        nargs="*",
        metavar="I J K",
        help="Display multiple marker spheres at specified voxel coordinates (i1 j1 k1 i2 j2 k2 ...). Must be groups of 3 integers.",
    )
    parser.add_argument(
        "--save-label",
        type=int,
        metavar="LABEL",
        help="Save specified label and point markers to NIfTI file (.nii.gz) and text file (.txt) for non-rigid registration. Specify the label value to save.",
    )
    parser.add_argument(
        "--point-labels",
        type=int,
        nargs="*",
        metavar="LABEL",
        help="Label values for each point when saving to NIfTI file. Must match number of points. Default: 1001, 1002, 1003, ...",
    )
    parser.add_argument(
        "--reg-labels",
        type=int,
        nargs="*",
        metavar="LABEL",
        help="Labels to find corresponding points after non-rigid registration. Must match number of points in standard file.",
    )
    return parser.parse_args(argv)


def main(argv: Optional[List[str]] = None) -> int:
    args = parse_args(argv)
    
    if not args.debug:
        # Suppress some VTK warnings in non-debug mode
        vtk.vtkObject.GlobalWarningDisplayOff()
    
    # Determine fast mode
    fast_mode = args.fast and not args.high_quality
    if args.high_quality:
        print("High quality mode enabled - slower but better rendering")
    elif fast_mode:
        print("Fast mode enabled - optimized for speed")
    
    try:
        # 处理自定义点参数
        voxel_point_from_point = tuple(args.point) if args.point else None
        voxel_point = tuple(args.voxel_point) if args.voxel_point else None
        
        # 处理多点参数
        multiple_points = []
        if args.points:
            if len(args.points) % 3 != 0:
                print("Error: --points requires groups of 3 integers (i j k coordinates).")
                print(f"Got {len(args.points)} integers, expected multiple of 3.")
                return 1
            
            # 将点坐标分组为 (i, j, k) 元组
            for i in range(0, len(args.points), 3):
                point = (args.points[i], args.points[i+1], args.points[i+2])
                multiple_points.append(point)
            print(f"Processing {len(multiple_points)} points from --points parameter")
        
        if voxel_point_from_point and voxel_point:
            print("Warning: Both --point and --voxel-point specified. Using --voxel-point.")
            voxel_point_from_point = None
        
        visualize_labels(
            seg_path=args.seg_file,
            background_labels=args.background,
            specific_labels=args.labels,
            high_quality=args.high_quality,
            show_endplates=args.endplates,
            custom_point=voxel_point_from_point,
            voxel_point=voxel_point,
            multiple_points=multiple_points,
            save_label=args.save_label,
            point_labels=args.point_labels,
            standard_file_path=args.standard_file,
            reg_labels=args.reg_labels,
        )
    except FileNotFoundError:
        print(f"[Error] File not found: {args.seg_file}", file=sys.stderr)
        return 1
    except Exception as exc:
        print(f"[Error] {exc}", file=sys.stderr)
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1
    return 0


if __name__ == "__main__":
    raise SystemExit(main())


