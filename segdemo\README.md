# CT分割标签可视化工具

使用SimpleITK和VTK读取并可视化CT影像分割后的NIfTI文件。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 主要可视化脚本

```bash
# 基本用法（显示所有非背景标签）
python visualize_labels_vtk.py your_segmentation.nii.gz

# 只显示特定标签（例如：25, 26, 27, 28）- 自动优化加载速度
python visualize_labels_vtk.py your_segmentation.nii.gz --labels 25 26 27 28

# 显示椎体并检测上终板中点（L4, L5, S1）
python visualize_labels_vtk.py your_segmentation.nii.gz --labels 26 27 28 --endplates

# 在指定体素坐标显示标记点
python visualize_labels_vtk.py your_segmentation.nii.gz --labels 26 27 28 --point 244 243 275

# 在指定体素坐标显示标记点（推荐用于调试）
python visualize_labels_vtk.py your_segmentation.nii.gz --labels 26 27 28 --voxel-point 244 243 275

# 显示多个标记点（不同颜色区分）
python visualize_labels_vtk.py your_segmentation.nii.gz --labels 26 27 28 --points 244 243 275 300 200 250 150 180 300

# 忽略特定背景标签 
python visualize_labels_vtk.py your_segmentation.nii.gz --background 0 255

# 启用调试输出
python visualize_labels_vtk.py your_segmentation.nii.gz --debug
```

### 2. 测试和诊断脚本

```bash
# 测试基本VTK功能
python test_visualization.py --test-basic

# 分析NIfTI文件信息
python test_visualization.py --file your_segmentation.nii.gz

# 使用合成测试数据
python test_visualization.py --test-volume
```

## 功能特点

- **智能数据过滤**: 指定标签时自动过滤掉其他器官数据，大幅减少加载时间
- **自动性能优化**: 使用多边形简化技术加速渲染
- **椎体上终板检测**: 自动检测L4(28)、L5(27)、S1(26)的上终板中点并用白色球体标记
- 自动检测所有非背景标签
- 为每个标签生成不同颜色的3D表面
- 支持交互式3D查看（旋转、缩放、平移）
- 详细的调试信息输出
- 正确处理医学影像的坐标系和方向

## 椎体上终板检测

脊柱分析的专用功能，支持自动检测腰椎和骶椎的上终板中点：

```bash
# 检测并显示L4, L5, S1的上终板中点
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --endplates
```

### 检测算法
- 分析椎体的Z轴分布，定位上方25%区域作为上终板区域  
- 在上终板区域寻找最大连通组件的重心
- 输出世界坐标系中的精确位置 (x, y, z) mm
- 用白色球体（半径3mm）在3D视图中标记位置

## 自定义点标记

支持在指定的体素坐标位置显示标记点：

```bash
# 单个点标记
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --point 244 243 275

# 单个点标记（推荐用于调试坐标系问题）
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --voxel-point 244 243 275

# 多个点标记（每3个数字为一组坐标 i j k，自动分配不同颜色）
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --points 244 243 275 300 200 250 150 180 300

# 同时显示椎体、终板中点和自定义点
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --endplates --voxel-point 244 243 275

# 结合多个功能：椎体 + 终板检测 + 多个自定义点
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --endplates --points 244 243 275 300 200 250

# 保存标签和点标记用于非刚性配准
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --points 244 243 275 257 214 187 231 214 195 --save-label 27 --point-labels 2001 2002 2003

# 保存标签和点标记（使用默认点标签值）
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --points 244 243 275 257 214 187 231 214 195 --save-label 27

# 使用标准文件进行配准，找到对应标签中的点
python visualize_labels_vtk.py your_file.nii.gz standard_27.nii.gz --labels 25 26 27 28 --high-quality --reg-labels 26 28

### 坐标系选择
- **`--point X Y Z`**: 体素坐标系（整数索引）- 用于精确的体素位置定位
- **`--voxel-point I J K`**: 体素坐标系（整数索引）- 用于调试和精确定位
- **`--points I1 J1 K1 I2 J2 K2 ...`**: 多个体素坐标（每3个数字为一组）- 同时显示多个标记点
- **`--save-label LABEL`**: 保存指定标签和点标记到NIfTI文件，用于非刚性配准
- **坐标转换**: 程序会显示详细的坐标转换信息帮助调试

### 标记特点
- **单个点**: 红色球体（半径4mm）
- **多个点**: 自动分配不同颜色（红、蓝、绿、橙、紫、黄等），最多支持18种预定义颜色
- **坐标验证**: 自动检查坐标是否在图像范围内
- **调试信息**: 显示图像的origin、spacing、size和direction信息
- **颜色识别**: 程序会输出每个点的颜色信息便于识别

## 标签和点标记保存功能

支持将指定标签和点标记一起保存为NIfTI文件和文本文件，用于后续的非刚性配准：

### 保存参数
- **`--save-label LABEL`**: 指定要保存的标签值（如27）
- **`--point-labels LABEL1 LABEL2 LABEL3`**: 为每个点指定标签值（可选，默认：1001, 1002, 1003, ...）

### 输出文件
- **`label_LABEL_with_points.nii.gz`**: NIfTI格式文件，包含指定标签区域和点标记
- **`label_LABEL_with_points.txt`**: 文本格式的详细信息文件，包含标签值、体素坐标、世界坐标和点标签值

### 使用场景
- **非刚性配准**: 将保存的NIfTI文件作为模板，与其他影像进行配准
- **标签+点标记**: 同时保存椎体标签和关键点标记，便于整体配准
- **坐标记录**: 文本文件记录标签信息和所有点的详细信息，便于后续分析
- **标签管理**: 可以为每个点分配唯一的标签值，便于识别

### 文件格式说明
```
# Label 27 with Point Markers for Non-rigid Registration
# Format: Label_Value, Point_ID, Voxel_Coordinates(i,j,k), World_Coordinates(x,y,z)_mm, Point_Label_Value
# Generated by CT Segmentation Labels Visualization Tool

Main_Label: 27
Number_of_Points: 3

Point_01: Voxel( 244,  243,  275) World( 123.45,  234.56,  345.67) Point_Label=2001
Point_02: Voxel( 257,  214,  187) World( 156.78,  267.89,  378.90) Point_Label=2002
Point_03: Voxel( 231,  214,  195) World( 189.12,  301.23,  412.34) Point_Label=2003
```

## 配准功能

支持使用标准文件进行非刚性配准，找到对应标签中的点标记：

### 配准参数
- **`standard_file`**: 标准NIfTI文件路径（包含标签和点标记）
- **`--reg-labels LABEL1 LABEL2 LABEL3`**: 要在目标图像中查找对应点的标签值

### 工作流程
1. **标准文件**: 包含标签27和三个点标记的NIfTI文件
2. **配准过程**: 将标准文件与目标图像进行配准
3. **点变换**: 将标准文件中的点坐标变换到目标图像
4. **结果显示**: 在目标图像中显示配准后的点标记

### 使用示例
```bash
# 使用标准文件进行配准
python visualize_labels_vtk.py target_file.nii.gz standard_27.nii.gz --labels 25 26 27 28 --high-quality --reg-labels 26 28
```

**功能说明**：
- 加载目标图像（显示标签25、26、27、28）
- 从标准文件 `standard_27.nii.gz` 中提取三个点标记
- 执行配准（当前为简化版本）
- 在目标图像中显示配准后的点，对应标签26和28

### 注意事项
- 当前实现使用简化的配准方法
- 对于精确配准，建议使用专业的配准软件
- 配准后的点会以不同颜色的球体显示
- 程序会输出详细的配准信息和点坐标

## 性能优化

```bash
# 默认快速模式（推荐）- 30%多边形简化，加速渲染
python visualize_labels_vtk.py your_file.nii.gz --labels 25 26 27 28

# 高质量模式 - 保留完整几何细节，较慢但更精细
python visualize_labels_vtk.py your_file.nii.gz --labels 25 26 27 28 --high-quality
```

### 模式对比
- **快速模式**: 30%多边形简化 → 更流畅的交互，略微降低细节
- **高质量模式**: 保留100%多边形 → 最佳视觉效果，可能较慢

## 交互控制

- **左键拖拽**: 旋转视角
- **右键拖拽**: 缩放
- **中键拖拽**: 平移
- **Q键或关闭窗口**: 退出

## 故障排除

如果渲染有问题，请：

1. 先运行测试脚本确认VTK工作正常
2. 使用`--debug`参数查看详细信息
3. 检查分割文件的标签值范围
4. 确认文件格式正确（.nii或.nii.gz）

## 依赖要求

- Python 3.7+
- SimpleITK 2.2+
- VTK 9.0+
- NumPy 1.20+
