# 配准功能使用示例

## 功能说明
配准功能允许你使用标准文件（包含标签和点标记）来找到目标图像中对应标签的点位置。

## 基本用法

### 1. 配准命令格式
```bash
python visualize_labels_vtk.py target_file.nii.gz standard_file.nii.gz --labels LABELS --reg-labels REG_LABELS
```

### 2. 具体示例
```bash
python visualize_labels_vtk.py segdemo/raw.nii segdemo/standard_27.nii.gz --labels 25 26 27 28 --high-quality --reg-labels 26 28
```

**参数说明**：
- `segdemo/raw.nii`: 目标图像文件
- `segdemo/standard_27.nii.gz`: 标准文件（包含标签27和三个点标记）
- `--labels 25 26 27 28`: 在目标图像中显示的标签
- `--high-quality`: 高质量渲染模式
- `--reg-labels 26 28`: 要在目标图像中查找对应点的标签

## 工作流程

### 步骤1：准备标准文件
首先需要有一个包含标签和点标记的标准文件：
```bash
# 创建标准文件（包含标签27和三个点）
python visualize_labels_vtk.py your_standard.nii.gz --labels 27 --points 244 243 275 257 214 187 231 214 195 --save-label 27 --point-labels 2001 2002 2003
```

这会生成：
- `label_27_with_points.nii.gz` - 标准文件
- `label_27_with_points.txt` - 坐标信息

### 步骤2：执行配准
使用标准文件进行配准：
```bash
python visualize_labels_vtk.py target_image.nii.gz label_27_with_points.nii.gz --labels 25 26 27 28 --reg-labels 26 28
```

### 步骤3：查看结果
程序会：
1. 加载目标图像并显示指定标签
2. 从标准文件中提取三个点标记
3. 执行配准（当前为简化版本）
4. 在目标图像中显示配准后的点

## 输出信息

### 控制台输出
```
=== Registration Mode ===
Standard file: segdemo/standard_27.nii.gz
Registration labels: [26, 28]

=== Extracting Points from Standard File ===
Standard file: segdemo/standard_27.nii.gz
Unique values in standard file: [0, 27, 2001, 2002, 2003]
Main label: 27
Point labels found: [2001, 2002, 2003]
Point label 2001: Voxel (244, 243, 275)
Point label 2002: Voxel (257, 214, 187)
Point label 2003: Voxel (231, 214, 195)
Extracted 3 points from standard file

Found 3 points in standard file

=== Performing Simple Registration ===
Note: This is a simplified registration. For accurate results, use dedicated registration software.
The current implementation will use the original point positions.

=== Transforming Points to Target Image ===
Point 1: Original (244, 243, 275) -> Transformed (244, 243, 275)
Point 2: Original (257, 214, 187) -> Transformed (257, 214, 187)
Point 3: Original (231, 214, 195) -> Transformed (231, 214, 195)
Note: Points are not actually transformed due to simplified registration.

Registration point 1: Voxel (244, 243, 275) -> World (123.45, 234.56, 345.67)
  Corresponds to label: 26
Registration point 2: Voxel (257, 214, 187) -> World (156.78, 267.89, 378.90)
  Corresponds to label: 28
Registration point 3: Voxel (231, 214, 195) -> World (189.12, 301.23, 412.34)
```

### 3D可视化
- 目标图像中的标签25、26、27、28会以不同颜色显示
- 配准后的三个点会以不同颜色的球体标记
- 每个点对应一个标签（26、28等）

## 注意事项

### 当前限制
1. **简化配准**: 当前实现使用简化的配准方法
2. **点变换**: 点坐标没有进行实际的几何变换
3. **精度**: 对于精确的医学影像配准，建议使用专业软件

### 改进建议
1. **真实配准**: 集成SimpleITK的配准算法
2. **点变换**: 实现真实的坐标变换
3. **配准质量**: 添加配准质量评估

### 使用建议
1. **标准文件**: 确保标准文件包含正确的标签和点标记
2. **标签匹配**: `--reg-labels` 的数量必须与标准文件中的点数量匹配
3. **坐标系**: 确保目标图像和标准文件使用相同的坐标系

## 完整工作流程示例

```bash
# 1. 创建标准文件
python visualize_labels_vtk.py standard_image.nii.gz --labels 27 --points 244 243 275 257 214 187 231 214 195 --save-label 27 --point-labels 2001 2002 2003

# 2. 执行配准
python visualize_labels_vtk.py target_image.nii.gz label_27_with_points.nii.gz --labels 25 26 27 28 --high-quality --reg-labels 26 28

# 3. 查看配准结果
# 程序会显示目标图像和配准后的点标记
```

这个功能为医学影像分析提供了一个基础框架，你可以在此基础上集成更精确的配准算法。

