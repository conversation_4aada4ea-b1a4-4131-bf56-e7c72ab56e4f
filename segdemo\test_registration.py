#!/usr/bin/env python3
"""
测试配准功能的脚本
"""

import SimpleITK as sitk
import numpy as np

def test_simple_registration():
    """测试简单的配准功能"""
    print("=== Testing Simple Registration ===")
    
    # 创建测试图像
    size = (64, 64, 64)
    spacing = (1.0, 1.0, 1.0)
    origin = (0.0, 0.0, 0.0)
    
    # 源图像：中心有一个球体
    source_array = np.zeros(size, dtype=np.uint8)
    center = (32, 32, 32)
    radius = 15
    
    for i in range(size[0]):
        for j in range(size[1]):
            for k in range(size[2]):
                if np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2) <= radius:
                    source_array[i, j, k] = 1
    
    source_image = sitk.GetImageFromArray(source_array)
    source_image.SetSpacing(spacing)
    source_image.SetOrigin(origin)
    
    # 目标图像：偏移的球体
    target_array = np.zeros(size, dtype=np.uint8)
    offset_center = (40, 40, 40)
    
    for i in range(size[0]):
        for j in range(size[1]):
            for k in range(size[2]):
                if np.sqrt((i-offset_center[0])**2 + (j-offset_center[1])**2 + (k-offset_center[2])**2) <= radius:
                    target_array[i, j, k] = 1
    
    target_image = sitk.GetImageFromArray(target_array)
    target_image.SetSpacing(spacing)
    target_image.SetOrigin(origin)
    
    print(f"Source image size: {source_image.GetSize()}")
    print(f"Target image size: {target_image.GetSize()}")
    print(f"Source sum: {np.sum(source_array)}")
    print(f"Target sum: {np.sum(target_array)}")
    
    # 测试配准
    try:
        from visualize_labels_vtk import perform_simple_registration
        
        print("\n--- Testing Registration ---")
        transform = perform_simple_registration(source_image, target_image, [1], [1])
        
        print(f"Transform type: {type(transform).__name__}")
        
        # 测试点变换
        test_point = (32, 32, 32)
        print(f"\nTest point: {test_point}")
        
        # 转换为物理坐标
        source_physical = source_image.TransformIndexToPhysicalPoint(test_point)
        print(f"Source physical: {source_physical}")
        
        # 应用变换
        transformed_physical = transform.TransformPoint(source_physical)
        print(f"Transformed physical: {transformed_physical}")
        
        # 转换回体素坐标
        target_voxel = target_image.TransformPhysicalPointToIndex(transformed_physical)
        target_voxel = tuple(int(round(coord)) for coord in target_voxel)
        print(f"Target voxel: {target_voxel}")
        
        # 计算位移
        displacement = np.sqrt(sum((np.array(test_point) - np.array(target_voxel))**2))
        print(f"Displacement: {displacement:.1f} voxels")
        
        if displacement > 0:
            print("✓ Registration is working - points are being transformed!")
        else:
            print("✗ Registration is not working - no displacement detected")
            
    except ImportError as e:
        print(f"Import error: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_simple_registration()

