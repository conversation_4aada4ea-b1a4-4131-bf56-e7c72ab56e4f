# 点标记保存功能使用示例

## 功能说明
新的 `--save-points` 功能可以将指定的点标记保存为NIfTI文件和文本文件，用于后续的非刚性配准。

## 基本用法

### 1. 保存三个点标记（使用默认标签值）
```bash
python visualize_labels_vtk.py segdemo/raw.nii --labels 25 26 27 28 --high-quality --points 244 243 275 257 214 187 231 214 195 --save-points standard_labels
```

**输出文件：**
- `standard_labels.nii.gz` - 包含三个点标记的NIfTI文件
- `standard_labels.txt` - 包含坐标信息的文本文件

**默认标签值：**
- 点1: 标签 1001
- 点2: 标签 1002  
- 点3: 标签 1003

### 2. 使用自定义标签值
```bash
python visualize_labels_vtk.py segdemo/raw.nii --labels 25 26 27 28 --high-quality --points 244 243 275 257 214 187 231 214 195 --save-points standard_labels --point-labels 2001 2002 2003
```

**自定义标签值：**
- 点1: 标签 2001
- 点2: 标签 2002
- 点3: 标签 2003

## 文件格式

### NIfTI文件 (.nii.gz)
- 与原始图像相同的坐标系和分辨率
- 每个点用指定的标签值标记
- 背景值为0

### 文本文件 (.txt)
```
# Point Markers for Non-rigid Registration
# Format: Point_ID, Voxel_Coordinates(i,j,k), World_Coordinates(x,y,z)_mm, Label_Value
# Generated by CT Segmentation Labels Visualization Tool

Point_01: Voxel( 244,  243,  275) World( 123.45,  234.56,  345.67) Label=1001
Point_02: Voxel( 257,  214,  187) World( 156.78,  267.89,  378.90) Label=1002
Point_03: Voxel( 231,  214,  195) World( 189.12,  301.23,  412.34) Label=1003
```

## 非刚性配准应用

### 1. 使用保存的NIfTI文件作为模板
```bash
# 在其他配准软件中使用 standard_labels.nii.gz 作为模板
# 该文件包含三个点标记，每个点有唯一的标签值
```

### 2. 坐标信息参考
- 文本文件提供详细的坐标信息
- 包含体素坐标和世界坐标
- 便于验证和调试配准结果

## 注意事项

1. **坐标数量**: `--points` 参数必须是3的倍数（每3个数字代表一个点）
2. **标签数量**: 如果使用 `--point-labels`，标签数量必须与点数量匹配
3. **文件覆盖**: 如果输出文件已存在，会被覆盖
4. **坐标系**: 保存的NIfTI文件保持与原始图像相同的坐标系信息

## 完整示例

```bash
# 显示椎体标签，标记三个点，并保存用于配准
python visualize_labels_vtk.py segdemo/raw.nii \
  --labels 25 26 27 28 \
  --high-quality \
  --points 244 243 275 257 214 187 231 214 195 \
  --save-points standard_labels \
  --point-labels 2001 2002 2003
```

这个命令会：
1. 加载并显示标签25、26、27、28的3D表面
2. 在指定位置显示三个不同颜色的球体标记
3. 保存 `standard_labels.nii.gz` 和 `standard_labels.txt` 文件
4. 为每个点分配标签值2001、2002、2003

