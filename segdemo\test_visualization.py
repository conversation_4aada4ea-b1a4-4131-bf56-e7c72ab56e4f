#!/usr/bin/env python3
"""
简单的测试脚本，用于调试VTK可视化问题
"""
import sys
import numpy as np

try:
    import SimpleITK as sitk
    import vtk
    from vtk.util import numpy_support
except ImportError as e:
    print(f"Missing required library: {e}")
    sys.exit(1)


def test_basic_vtk():
    """测试基本的VTK功能"""
    print("Testing basic VTK functionality...")
    
    # 创建一个简单的立方体
    cube = vtk.vtkCubeSource()
    cube.Update()
    
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(cube.GetOutputPort())
    
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red
    
    renderer = vtk.vtkRenderer()
    renderer.AddActor(actor)
    renderer.SetBackground(0.1, 0.1, 0.1)
    
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(600, 600)
    render_window.SetWindowName("VTK Test - Red Cube")
    
    interactor = vtk.vtkRenderWindowInteractor()
    interactor.SetRenderWindow(render_window)
    
    render_window.Render()
    print("Basic VTK test passed. You should see a red cube.")
    print("Press 'q' to continue...")
    interactor.Start()


def analyze_nifti_file(filepath):
    """分析NIfTI文件的基本信息"""
    print(f"\nAnalyzing file: {filepath}")
    
    try:
        image = sitk.ReadImage(filepath)
    except Exception as e:
        print(f"Error reading file: {e}")
        return None
    
    # 基本信息
    size = image.GetSize()
    spacing = image.GetSpacing()  
    origin = image.GetOrigin()
    direction = image.GetDirection()
    pixel_type = image.GetPixelID()
    
    print(f"Image dimensions (x,y,z): {size}")
    print(f"Voxel spacing: {spacing}")
    print(f"Origin: {origin}")
    print(f"Pixel type: {sitk.GetPixelIDValueAsString(pixel_type)}")
    print(f"Number of components: {image.GetNumberOfComponentsPerPixel()}")
    
    # 数组分析
    array = sitk.GetArrayFromImage(image)
    print(f"Array shape (z,y,x): {array.shape}")
    print(f"Array dtype: {array.dtype}")
    print(f"Value range: [{array.min()}, {array.max()}]")
    
    # 唯一值分析
    unique_values = np.unique(array)
    print(f"Number of unique values: {len(unique_values)}")
    print(f"Unique values: {unique_values[:20]}{'...' if len(unique_values) > 20 else ''}")
    
    # 非零值分析
    nonzero_values = unique_values[unique_values != 0]
    print(f"Non-zero labels: {nonzero_values}")
    
    return image


def create_test_volume():
    """创建一个简单的测试标签体积"""
    print("\nCreating test volume...")
    
    # 创建64x64x64的测试体积
    size = (64, 64, 64)
    volume = np.zeros(size, dtype=np.uint16)
    
    # 添加几个简单的标签区域
    # 标签1: 小立方体
    volume[20:40, 20:40, 20:40] = 1
    
    # 标签2: 球体
    center = np.array([32, 32, 32])
    radius = 15
    for z in range(size[2]):
        for y in range(size[1]):
            for x in range(size[0]):
                if np.linalg.norm([x, y, z] - center) < radius:
                    if volume[z, y, x] == 0:  # 不覆盖立方体
                        volume[z, y, x] = 2
    
    # 标签3: 另一个小区域
    volume[10:20, 50:60, 10:20] = 3
    
    # 转换为SimpleITK图像
    image = sitk.GetImageFromArray(volume)
    image.SetSpacing([1.0, 1.0, 1.0])
    image.SetOrigin([0.0, 0.0, 0.0])
    
    print(f"Test volume created with labels: {np.unique(volume)}")
    return image


def visualize_test_volume():
    """可视化测试体积"""
    print("\nVisualizing test volume...")
    
    image = create_test_volume()
    
    # 转换为VTK格式
    array = sitk.GetArrayFromImage(image)
    size = image.GetSize()
    
    # 创建VTK图像
    vtk_image = vtk.vtkImageData()
    vtk_image.SetDimensions(size)
    vtk_image.SetSpacing(image.GetSpacing())
    vtk_image.SetOrigin(image.GetOrigin())
    
    # 转换数组格式
    array_xyz = np.transpose(array, (2, 1, 0))
    flat_array = np.ravel(array_xyz, order='F')
    
    vtk_array = numpy_support.numpy_to_vtk(flat_array, deep=True)
    vtk_array.SetName("Labels")
    vtk_image.GetPointData().SetScalars(vtk_array)
    
    # 创建表面
    renderer = vtk.vtkRenderer()
    renderer.SetBackground(0.2, 0.2, 0.2)
    
    labels = [1, 2, 3]
    colors = [(1.0, 0.0, 0.0), (0.0, 1.0, 0.0), (0.0, 0.0, 1.0)]  # Red, Green, Blue
    
    for label, color in zip(labels, colors):
        print(f"Creating surface for label {label}...")
        
        marching_cubes = vtk.vtkMarchingCubes()
        marching_cubes.SetInputData(vtk_image)
        marching_cubes.SetValue(0, label)
        marching_cubes.Update()
        
        polydata = marching_cubes.GetOutput()
        print(f"  Points: {polydata.GetNumberOfPoints()}, Cells: {polydata.GetNumberOfCells()}")
        
        if polydata.GetNumberOfPoints() > 0:
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(polydata)
            
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(color)
            actor.GetProperty().SetOpacity(0.8)
            
            renderer.AddActor(actor)
    
    # 渲染窗口
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(800, 600)
    render_window.SetWindowName("Test Volume Visualization")
    
    interactor = vtk.vtkRenderWindowInteractor()
    interactor.SetRenderWindow(render_window)
    
    renderer.ResetCamera()
    render_window.Render()
    
    print("Test visualization created. Press 'q' to exit.")
    interactor.Start()


def main():
    import argparse
    parser = argparse.ArgumentParser(description="Test VTK visualization")
    parser.add_argument("--file", help="NIfTI file to analyze")
    parser.add_argument("--test-basic", action="store_true", help="Test basic VTK")
    parser.add_argument("--test-volume", action="store_true", help="Test with synthetic volume")
    
    args = parser.parse_args()
    
    if args.test_basic:
        test_basic_vtk()
    
    if args.file:
        analyze_nifti_file(args.file)
    
    if args.test_volume:
        visualize_test_volume()
    
    if not any([args.test_basic, args.file, args.test_volume]):
        print("Usage examples:")
        print("  python test_visualization.py --test-basic")
        print("  python test_visualization.py --file your_segmentation.nii.gz")
        print("  python test_visualization.py --test-volume")


if __name__ == "__main__":
    main()

