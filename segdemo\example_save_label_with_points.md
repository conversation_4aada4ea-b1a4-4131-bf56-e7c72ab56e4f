# 标签和点标记保存功能使用示例

## 功能说明
新的 `--save-label` 功能可以将指定的标签（如标签27）和对应的点标记一起保存为NIfTI文件和文本文件，用于后续的非刚性配准。

## 基本用法

### 1. 保存标签27和三个点标记（使用自定义标签值）
```bash
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --points 244 243 275 257 214 187 231 214 195 --save-label 27 --point-labels 2001 2002 2003
```

**输出文件：**
- `label_27_with_points.nii.gz` - 包含标签27区域和三个点标记的NIfTI文件
- `label_27_with_points.txt` - 包含标签信息和点坐标的文本文件

**标签值：**
- 主标签: 27（椎体标签）
- 点1: 标签 2001
- 点2: 标签 2002  
- 点3: 标签 2003

### 2. 使用默认点标签值
```bash
python visualize_labels_vtk.py your_file.nii.gz --labels 26 27 28 --points 244 243 275 257 214 187 231 214 195 --save-label 27
```

**默认标签值：**
- 主标签: 27（椎体标签）
- 点1: 标签 1001
- 点2: 标签 1002
- 点3: 标签 1003

## 文件格式

### NIfTI文件 (.nii.gz)
- 与原始图像相同的坐标系和分辨率
- 包含指定标签（如27）的完整区域
- 在指定位置用点标签值标记三个点
- 背景值为0

### 文本文件 (.txt)
```
# Label 27 with Point Markers for Non-rigid Registration
# Format: Label_Value, Point_ID, Voxel_Coordinates(i,j,k), World_Coordinates(x,y,z)_mm, Point_Label_Value
# Generated by CT Segmentation Labels Visualization Tool

Main_Label: 27
Number_of_Points: 3

Point_01: Voxel( 244,  243,  275) World( 123.45,  234.56,  345.67) Point_Label=2001
Point_02: Voxel( 257,  214,  187) World( 156.78,  267.89,  378.90) Point_Label=2002
Point_03: Voxel( 231,  214,  195) World( 189.12,  301.23,  412.34) Point_Label=2003
```

## 非刚性配准应用

### 1. 使用保存的NIfTI文件作为模板
```bash
# 在其他配准软件中使用 label_27_with_points.nii.gz 作为模板
# 该文件包含：
# - 标签27的完整椎体区域
# - 三个关键点标记，每个点有唯一的标签值
```

### 2. 配准优势
- **整体配准**: 同时包含椎体形状和关键点信息
- **点标记**: 每个点有唯一标签值，便于识别和跟踪
- **坐标系**: 保持与原始图像相同的坐标系信息

### 3. 坐标信息参考
- 文本文件提供详细的标签和点坐标信息
- 包含体素坐标和世界坐标
- 便于验证和调试配准结果

## 注意事项

1. **坐标数量**: `--points` 参数必须是3的倍数（每3个数字代表一个点）
2. **标签数量**: 如果使用 `--point-labels`，标签数量必须与点数量匹配
3. **文件覆盖**: 如果输出文件已存在，会被覆盖
4. **坐标系**: 保存的NIfTI文件保持与原始图像相同的坐标系信息
5. **文件命名**: 自动生成文件名 `label_LABEL_with_points.nii.gz` 和 `label_LABEL_with_points.txt`

## 完整示例

```bash
# 显示椎体标签，标记三个点，并保存标签27和点标记用于配准
python visualize_labels_vtk.py your_file.nii.gz \
  --labels 25 26 27 28 \
  --high-quality \
  --points 244 243 275 257 214 187 231 214 195 \
  --save-label 27 \
  --point-labels 2001 2002 2003
```

这个命令会：
1. 加载并显示标签25、26、27、28的3D表面
2. 在指定位置显示三个不同颜色的球体标记
3. 保存 `label_27_with_points.nii.gz` 文件（包含标签27区域和三个点标记）
4. 保存 `label_27_with_points.txt` 文件（包含标签信息和点坐标）
5. 为每个点分配标签值2001、2002、2003

## 工作流程建议

1. **标记阶段**: 在你的标准影像上标记三个关键点
2. **保存阶段**: 使用 `--save-label` 保存标签和点标记
3. **配准阶段**: 将保存的NIfTI文件作为模板进行非刚性配准
4. **结果验证**: 使用文本文件中的坐标信息验证配准结果
5. **点跟踪**: 通过点标签值跟踪配准后的点位置

